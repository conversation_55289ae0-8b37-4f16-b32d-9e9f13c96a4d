'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'

const Coronation = () => {
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  })

  useEffect(() => {
    const targetDate = new Date('2025-04-15T10:00:00').getTime()

    const timer = setInterval(() => {
      const now = new Date().getTime()
      const difference = targetDate - now

      const days = Math.floor(difference / (1000 * 60 * 60 * 24))
      const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
      const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60))
      const seconds = Math.floor((difference % (1000 * 60)) / 1000)

      setTimeLeft({ days, hours, minutes, seconds })

      if (difference < 0) {
        clearInterval(timer)
        setTimeLeft({ days: 0, hours: 0, minutes: 0, seconds: 0 })
      }
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  const events = [
    {
      day: 'Day 1: April 14, 2025',
      icon: 'fas fa-sun',
      schedule: [
        { time: '9:00 AM', event: 'Traditional Purification Ceremony', location: 'Manhyia Palace' },
        { time: '2:00 PM', event: 'Royal Procession', location: 'Accra City Center' },
        { time: '7:00 PM', event: 'Cultural Performances', location: 'National Theatre' }
      ]
    },
    {
      day: 'Day 2: April 15, 2025',
      icon: 'fas fa-crown',
      schedule: [
        { time: '10:00 AM', event: 'Coronation Ceremony', location: 'Accra International Conference Centre' },
        { time: '2:00 PM', event: 'Royal Address', location: 'Independence Square' },
        { time: '7:00 PM', event: 'Royal Banquet', location: 'State House' }
      ]
    }
  ]

  return (
    <section id="coronation" className="py-20 bg-royalBlue relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="royal-pattern w-full h-full"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-serif font-bold text-white mb-4">Coronation 2025</h2>
          <div className="w-24 h-1 bg-royalGold mx-auto mb-4"></div>
          <p className="max-w-3xl mx-auto text-white/80 leading-relaxed">
            Join us for the historic coronation ceremony of His Royal Majesty, a once-in-a-generation celebration of Ghana's royal heritage.
          </p>
        </motion.div>

        {/* Main Event Card */}
        <motion.div 
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="bg-white rounded-2xl shadow-2xl overflow-hidden mb-16"
        >
          <div className="flex flex-col md:flex-row">
            {/* Countdown Section */}
            <div className="md:w-1/2 bg-gradient-to-br from-royalGold/20 to-royalGold/10 p-8 flex items-center justify-center">
              <div className="text-center">
                <motion.div 
                  initial={{ scale: 0.8 }}
                  whileInView={{ scale: 1 }}
                  transition={{ duration: 0.5 }}
                  className="text-royalGold text-6xl mb-6"
                >
                  <i className="fas fa-crown"></i>
                </motion.div>
                <h3 className="text-2xl font-serif font-bold text-royalBlue mb-2">Royal Coronation</h3>
                <p className="text-royalBlue/70 mb-8">A Historic Celebration</p>
                
                {/* Countdown Timer */}
                <div className="flex justify-center space-x-4">
                  {Object.entries(timeLeft).map(([unit, value]) => (
                    <motion.div
                      key={unit}
                      initial={{ scale: 0 }}
                      whileInView={{ scale: 1 }}
                      transition={{ duration: 0.5, delay: 0.1 }}
                      className="bg-white rounded-lg shadow-lg p-4 text-center min-w-[80px]"
                    >
                      <motion.div 
                        key={value}
                        initial={{ y: -20, opacity: 0 }}
                        animate={{ y: 0, opacity: 1 }}
                        transition={{ duration: 0.3 }}
                        className="text-royalGold font-bold text-2xl"
                      >
                        {value}
                      </motion.div>
                      <div className="text-xs text-gray-600 capitalize">{unit}</div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </div>

            {/* Event Details */}
            <div className="md:w-1/2 p-8">
              <h3 className="text-xl font-bold text-royalBlue mb-4">Event Overview</h3>
              <p className="text-gray-700 mb-4 leading-relaxed">
                The coronation of His Royal Majesty will be a magnificent celebration of Ghana's cultural heritage and a momentous occasion in the nation's history. This once-in-a-generation event will blend ancient traditions with contemporary elements.
              </p>
              <p className="text-gray-700 mb-6 leading-relaxed">
                Distinguished guests from across Africa and around the globe will join Ghanaian citizens in witnessing this historic ceremony, which marks the beginning of a new era for the kingdom.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4">
                <motion.a
                  href="#schedule"
                  className="px-6 py-3 relative overflow-hidden text-royalBlue font-bold rounded-lg shadow-2xl border-2 border-yellow-300 group text-center"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  style={{
                    background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 25%, #ffd700 50%, #b8860b 75%, #ffd700 100%)',
                    boxShadow: '0 8px 32px rgba(255, 215, 0, 0.4), inset 0 2px 4px rgba(255, 255, 255, 0.3), inset 0 -2px 4px rgba(0, 0, 0, 0.2)'
                  }}
                >
                  {/* Shining animation overlay */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></div>
                  <span className="relative z-10 font-extrabold">
                    <i className="fas fa-calendar-alt mr-2"></i>
                    View Schedule
                  </span>
                </motion.a>
                <motion.a 
                  href="#tickets" 
                  className="px-6 py-3 bg-royalBlue text-white font-bold rounded-lg hover:bg-royalBlue/90 transition-all duration-300 text-center"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <i className="fas fa-ticket-alt mr-2"></i>
                  Get Tickets
                </motion.a>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Event Schedule */}
        <div id="schedule" className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {events.map((eventDay, dayIndex) => (
            <motion.div
              key={dayIndex}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: dayIndex * 0.2 }}
              viewport={{ once: true }}
              className="bg-white rounded-xl shadow-lg overflow-hidden"
            >
              <div className="bg-gradient-to-r from-royalBlue to-royalBlue/80 text-white p-6">
                <div className="flex items-center">
                  <div className="w-12 h-12 rounded-full bg-royalGold/20 flex items-center justify-center text-royalGold mr-4">
                    <i className={eventDay.icon}></i>
                  </div>
                  <h3 className="font-serif font-bold text-lg">{eventDay.day}</h3>
                </div>
              </div>
              
              <div className="p-6">
                <div className="space-y-6">
                  {eventDay.schedule.map((item, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.4, delay: index * 0.1 }}
                      className="relative pl-8 border-l-2 border-royalGold/30"
                    >
                      <div className="absolute -left-2 w-4 h-4 rounded-full bg-royalGold"></div>
                      <div>
                        <p className="text-sm font-bold text-gray-800">{item.time} - {item.event}</p>
                        <p className="text-xs text-gray-600 mt-1">
                          <i className="fas fa-map-marker-alt text-royalGold mr-1"></i>
                          {item.location}
                        </p>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Call to Action */}
        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <p className="text-white/80 mb-6">
            For accommodation and travel information, please contact our Royal Hospitality Team.
          </p>
          <motion.a
            href="#contact"
            className="inline-flex items-center px-8 py-4 relative overflow-hidden text-royalBlue font-bold rounded-lg shadow-2xl border-2 border-yellow-300 group"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            style={{
              background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 25%, #ffd700 50%, #b8860b 75%, #ffd700 100%)',
              boxShadow: '0 12px 40px rgba(255, 215, 0, 0.5), inset 0 3px 6px rgba(255, 255, 255, 0.4), inset 0 -3px 6px rgba(0, 0, 0, 0.3)'
            }}
          >
            {/* Shining animation overlay */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1200 ease-out"></div>
            <span className="relative z-10 font-extrabold">
              <i className="fas fa-phone mr-2"></i>
              Contact Hospitality Team
            </span>
          </motion.a>
        </motion.div>
      </div>
    </section>
  )
}

export default Coronation
