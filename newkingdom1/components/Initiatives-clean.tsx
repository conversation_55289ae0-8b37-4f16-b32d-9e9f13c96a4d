'use client'

import React from 'react'
import type { Initiative } from '@/types'

const Initiatives = () => {
  const initiatives: Initiative[] = [
    {
      title: 'Education for All',
      description: 'Providing quality education and scholarships to underprivileged children across Ghana.',
      icon: 'fas fa-graduation-cap',
      color: 'from-blue-500 to-blue-600',
      stats: '5,000+ Students Supported',
      image: '/images/education.jpg'
    },
    {
      title: 'Healthcare Access',
      description: 'Building medical facilities and providing healthcare services to rural communities.',
      icon: 'fas fa-heartbeat',
      color: 'from-red-500 to-red-600',
      stats: '50+ Clinics Established',
      image: '/images/healthcare.jpg'
    },
    {
      title: 'Economic Empowerment',
      description: 'Supporting small businesses and entrepreneurship through microfinance and training.',
      icon: 'fas fa-chart-line',
      color: 'from-green-500 to-green-600',
      stats: '2,000+ Businesses Funded',
      image: '/images/business.jpg'
    },
    {
      title: 'Cultural Preservation',
      description: 'Documenting and preserving traditional arts, crafts, and cultural practices.',
      icon: 'fas fa-palette',
      color: 'from-purple-500 to-purple-600',
      stats: '100+ Traditions Documented',
      image: '/images/culture.jpg'
    },
    {
      title: 'Environmental Conservation',
      description: 'Protecting natural resources and promoting sustainable development practices.',
      icon: 'fas fa-leaf',
      color: 'from-green-400 to-green-500',
      stats: '10,000+ Trees Planted',
      image: '/images/environment.jpg'
    },
    {
      title: 'Youth Development',
      description: 'Empowering young people through skills training and leadership programs.',
      icon: 'fas fa-users',
      color: 'from-orange-500 to-orange-600',
      stats: '3,000+ Youth Trained',
      image: '/images/youth.jpg'
    }
  ]

  return (
    <section id="initiatives" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-serif font-bold text-royalBlue mb-4">Royal Initiatives</h2>
          <div className="w-24 h-1 bg-royalGold mx-auto mb-8"></div>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto leading-relaxed">
            Our commitment to the people of Ghana extends beyond ceremony and tradition. 
            Through these initiatives, we work to improve lives and build a better future for all.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {initiatives.map((initiative, index) => (
            <div
              key={index}
              className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 hover:-translate-y-2"
            >
              {/* Header with Icon */}
              <div className={`bg-gradient-to-r ${initiative.color} p-6 text-white`}>
                <div className="flex items-center justify-between">
                  <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                    <i className={`${initiative.icon} text-xl`}></i>
                  </div>
                  <div className="text-right">
                    <p className="text-white/90 text-sm font-medium">{initiative.stats}</p>
                  </div>
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                <h3 className="text-xl font-bold text-royalBlue mb-3">{initiative.title}</h3>
                <p className="text-gray-700 leading-relaxed mb-4">{initiative.description}</p>
                
                <div className="flex items-center justify-between">
                  <button className="text-royalBlue font-semibold hover:text-royalBlue/80 transition-colors">
                    Learn More
                    <i className="fas fa-arrow-right ml-2"></i>
                  </button>
                  <div className="flex space-x-2">
                    <button className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center hover:bg-royalBlue hover:text-white transition-colors">
                      <i className="fas fa-share-alt text-xs"></i>
                    </button>
                    <button className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center hover:bg-royalBlue hover:text-white transition-colors">
                      <i className="fas fa-heart text-xs"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Impact Statistics */}
        <div className="mt-16 bg-white rounded-2xl shadow-lg p-8 md:p-12">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-serif font-bold text-royalBlue mb-4">Our Impact</h3>
            <p className="text-gray-700 leading-relaxed">
              Together, we are making a meaningful difference in the lives of Ghanaians across the nation.
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-3xl font-bold text-royalBlue mb-2">20,000+</div>
              <p className="text-gray-600">Lives Impacted</p>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-royalBlue mb-2">100+</div>
              <p className="text-gray-600">Communities Served</p>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-royalBlue mb-2">50+</div>
              <p className="text-gray-600">Partner Organizations</p>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-royalBlue mb-2">$2M+</div>
              <p className="text-gray-600">Funds Invested</p>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-royalBlue to-royalBlue/90 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-serif font-bold mb-4">Join Our Mission</h3>
            <p className="text-white/90 mb-6 max-w-2xl mx-auto leading-relaxed">
              Your support helps us expand these vital programs and reach even more communities. 
              Together, we can build a stronger, more prosperous Ghana.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="px-6 py-3 bg-royalGold text-royalBlue font-bold rounded-lg hover:bg-royalGold/90 transition-colors">
                <i className="fas fa-hand-holding-heart mr-2"></i>
                Support Our Initiatives
              </button>
              <button className="px-6 py-3 bg-white/10 text-white font-bold rounded-lg hover:bg-white/20 transition-colors border border-white/30">
                <i className="fas fa-users mr-2"></i>
                Become a Volunteer
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Initiatives
