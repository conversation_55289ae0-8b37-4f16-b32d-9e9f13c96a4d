'use client'

import React from 'react'
import { motion } from 'framer-motion'
import type { GalleryImage } from '@/types'

const RoyalGallery = () => {
  const featuredImages: GalleryImage[] = [
    {
      title: 'Royal Palace at Sunset',
      description: 'The majestic Royal Palace illuminated by the golden hour',
      size: 'large'
    },
    {
      title: 'Traditional Kente Weaving',
      description: 'Master craftsmen creating the iconic Kente cloth',
      size: 'medium'
    },
    {
      title: 'Royal Guard Ceremony',
      description: 'The changing of the Royal Guard in traditional regalia',
      size: 'medium'
    },
    {
      title: 'Cultural Dance Performance',
      description: 'Traditional dancers performing at the royal court',
      size: 'small'
    },
    {
      title: 'Royal Banquet Hall',
      description: 'The grand banquet hall prepared for state dinners',
      size: 'small'
    },
    {
      title: 'Crown Jewels Display',
      description: 'The magnificent crown jewels of the Kingdom',
      size: 'small'
    }
  ]

  return (
    <section id="royal-gallery" className="py-20 relative overflow-hidden">
      {/* Background with gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-cream via-ivory to-cream"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/5 via-transparent to-royalBlue/5"></div>
      
      {/* Floating background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          animate={{ 
            x: [0, 250, 0],
            y: [0, -150, 0],
            rotate: [0, 360, 720]
          }}
          transition={{ 
            duration: 50,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute top-20 right-20 w-96 h-96 bg-gradient-to-br from-royalGold/8 to-transparent rounded-full blur-3xl"
        />
        <motion.div
          animate={{ 
            x: [0, -200, 0],
            y: [0, 100, 0],
            rotate: [0, -270, -540]
          }}
          transition={{ 
            duration: 42,
            repeat: Infinity,
            ease: "linear",
            delay: 15
          }}
          className="absolute bottom-20 left-20 w-80 h-80 bg-gradient-to-tl from-royalBlue/8 to-transparent rounded-full blur-3xl"
        />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-serif font-bold bg-gradient-to-r from-royalBlue to-royalBlue/80 bg-clip-text text-transparent mb-4">
            Royal Gallery
          </h2>
          <div className="w-32 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto leading-relaxed">
            Explore the rich cultural heritage and royal traditions of the Kingdom through our curated gallery of images, capturing moments of history, culture, and celebration.
          </p>
        </motion.div>



        {/* Featured Images Grid */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mb-16"
        >
          <div className="text-center mb-12">
            <h3 className="text-3xl font-serif font-bold text-royalBlue mb-4">Featured Images</h3>
            <div className="w-24 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-4"></div>
            <p className="text-gray-700 max-w-2xl mx-auto">
              A selection of our most captivating images showcasing the beauty and grandeur of the Royal Kingdom.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {featuredImages.map((image, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ scale: 1.05, y: -5 }}
                className={`group relative cursor-pointer ${
                  image.size === 'large' ? 'md:col-span-2 md:row-span-2' :
                  image.size === 'medium' ? 'md:col-span-2' : 'md:col-span-1'
                }`}
              >
                <div className={`bg-gradient-to-br from-gray-200 to-gray-300 rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 ${
                  image.size === 'large' ? 'h-80' :
                  image.size === 'medium' ? 'h-40' : 'h-32'
                }`}>
                  {/* Image Placeholder */}
                  <div className="w-full h-full bg-gradient-to-br from-royalBlue/20 to-royalGold/20 relative overflow-hidden">
                    <div className="absolute inset-0 flex items-center justify-center">
                      <i className="fas fa-image text-gray-400 text-4xl"></i>
                    </div>
                    
                    {/* Overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    
                    {/* Content */}
                    <div className="absolute bottom-0 left-0 right-0 p-4 text-white transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                      <h4 className="font-bold text-sm mb-1">{image.title}</h4>
                      <p className="text-xs text-white/90">{image.description}</p>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <div className="bg-gradient-to-r from-royalBlue/10 to-royalGold/10 backdrop-blur-lg rounded-3xl p-8 border border-white/30 shadow-2xl max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-royalBlue mb-4">Explore More</h3>
            <p className="text-gray-700 mb-6 leading-relaxed">
              Discover the complete collection of royal images, historical documents, and cultural artifacts in our comprehensive digital archive.
            </p>
            <motion.a
              href="/gallery"
              className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-royalGold to-yellow-500 text-royalBlue font-bold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <i className="fas fa-images mr-2"></i>
              View Full Gallery
            </motion.a>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default RoyalGallery
