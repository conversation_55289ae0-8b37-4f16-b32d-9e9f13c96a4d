'use client'

import React, { useState } from 'react'
import type { RSVPFormData } from '@/types/forms'

const RSVP = () => {
  const [formData, setFormData] = useState<RSVPFormData>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    country: '',
    events: [],
    attendanceType: '',
    reminderPreference: '',
    notes: ''
  })

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitMessage, setSubmitMessage] = useState('')

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked
      if (name === 'events') {
        setFormData(prev => ({
          ...prev,
          events: checked 
            ? [...prev.events, value]
            : prev.events.filter(event => event !== value)
        }))
      }
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }))
    }
  }

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    // Simulate form submission
    setTimeout(() => {
      setIsSubmitting(false)
      setSubmitMessage('Thank you for your RSVP! We will send you confirmation details soon.')
      setFormData({
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        country: '',
        events: [],
        attendanceType: '',
        reminderPreference: '',
        notes: ''
      })
    }, 2000)
  }

  const eventOptions = [
    'Traditional Purification Ceremony',
    'Royal Procession',
    'Cultural Performances',
    'Coronation Ceremony',
    'Royal Address',
    'Royal Banquet'
  ]

  return (
    <section id="rsvp" className="py-20 bg-gradient-to-br from-royalGold/10 to-royalGold/5">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-serif font-bold text-royalBlue mb-4">RSVP for Royal Events</h2>
          <div className="w-24 h-1 bg-royalGold mx-auto mb-8"></div>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto leading-relaxed">
            Secure your place at the historic coronation ceremony and related royal events. 
            Please complete this form to receive your official invitation and event details.
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-2xl shadow-xl p-8 md:p-12">
            {submitMessage && (
              <div className="mb-8 p-4 bg-green-100 border border-green-300 rounded-lg text-green-800">
                {submitMessage}
              </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Personal Information */}
              <div>
                <h3 className="text-xl font-bold text-royalBlue mb-4">Personal Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="firstName" className="block text-gray-700 font-medium mb-2">
                      First Name *
                    </label>
                    <input
                      type="text"
                      id="firstName"
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-royalBlue focus:ring-1 focus:ring-royalBlue"
                      placeholder="Your first name"
                    />
                  </div>
                  <div>
                    <label htmlFor="lastName" className="block text-gray-700 font-medium mb-2">
                      Last Name *
                    </label>
                    <input
                      type="text"
                      id="lastName"
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-royalBlue focus:ring-1 focus:ring-royalBlue"
                      placeholder="Your last name"
                    />
                  </div>
                </div>
              </div>

              {/* Contact Information */}
              <div>
                <h3 className="text-xl font-bold text-royalBlue mb-4">Contact Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="email" className="block text-gray-700 font-medium mb-2">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-royalBlue focus:ring-1 focus:ring-royalBlue"
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div>
                    <label htmlFor="phone" className="block text-gray-700 font-medium mb-2">
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-royalBlue focus:ring-1 focus:ring-royalBlue"
                      placeholder="+****************"
                    />
                  </div>
                </div>
                <div className="mt-6">
                  <label htmlFor="country" className="block text-gray-700 font-medium mb-2">
                    Country *
                  </label>
                  <input
                    type="text"
                    id="country"
                    name="country"
                    value={formData.country}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-royalBlue focus:ring-1 focus:ring-royalBlue"
                    placeholder="Your country"
                  />
                </div>
              </div>

              {/* Event Selection */}
              <div>
                <h3 className="text-xl font-bold text-royalBlue mb-4">Event Selection</h3>
                <p className="text-gray-600 mb-4">Select which events you would like to attend:</p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {eventOptions.map((event) => (
                    <label key={event} className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                      <input
                        type="checkbox"
                        name="events"
                        value={event}
                        checked={formData.events.includes(event)}
                        onChange={handleInputChange}
                        className="w-4 h-4 text-royalBlue border-gray-300 rounded focus:ring-royalBlue"
                      />
                      <span className="text-gray-700">{event}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Attendance Type */}
              <div>
                <label htmlFor="attendanceType" className="block text-gray-700 font-medium mb-2">
                  Attendance Type *
                </label>
                <select
                  id="attendanceType"
                  name="attendanceType"
                  value={formData.attendanceType}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-royalBlue focus:ring-1 focus:ring-royalBlue"
                >
                  <option value="">Select attendance type</option>
                  <option value="in-person">In-Person Attendance</option>
                  <option value="virtual">Virtual Attendance</option>
                  <option value="both">Both In-Person and Virtual</option>
                </select>
              </div>

              {/* Additional Information */}
              <div>
                <label htmlFor="notes" className="block text-gray-700 font-medium mb-2">
                  Special Requests or Notes
                </label>
                <textarea
                  id="notes"
                  name="notes"
                  value={formData.notes}
                  onChange={handleInputChange}
                  rows={4}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-royalBlue focus:ring-1 focus:ring-royalBlue resize-vertical"
                  placeholder="Any special dietary requirements, accessibility needs, or other requests..."
                ></textarea>
              </div>

              {/* Submit Button */}
              <div className="text-center pt-6">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="px-8 py-3 bg-royalBlue text-white font-bold rounded-lg hover:bg-royalBlue/90 transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? (
                    <>
                      <i className="fas fa-spinner fa-spin mr-2"></i>
                      Submitting RSVP...
                    </>
                  ) : (
                    <>
                      <i className="fas fa-paper-plane mr-2"></i>
                      Submit RSVP
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </section>
  )
}

export default RSVP
