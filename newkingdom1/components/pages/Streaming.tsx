'use client'

import { motion } from 'framer-motion'

const Streaming = () => {
  const liveStreams = [
    {
      title: 'Royal Coronation Ceremony - Live',
      description: 'Watch the historic coronation ceremony live from the Royal Palace',
      date: 'March 15, 2025',
      time: '10:00 AM GMT',
      status: 'upcoming',
      viewers: '50K+ Expected',
      duration: '3 hours',
      platforms: ['YouTube', 'Facebook', 'Royal Website']
    }
  ]

  const upcomingStreams = [
    {
      title: 'Cultural Heritage Festival',
      description: 'Experience traditional Ghanaian culture through live performances and exhibitions',
      date: 'April 20, 2025',
      time: '2:00 PM GMT',
      status: 'upcoming',
      duration: '2 hours',
      platforms: ['YouTube', 'Facebook']
    },
    {
      title: 'Royal Business Summit',
      description: 'Join business leaders discussing investment opportunities in Ghana',
      date: 'May 10, 2025',
      time: '9:00 AM GMT',
      status: 'upcoming',
      duration: '4 hours',
      platforms: ['LinkedIn', 'YouTube']
    },
    {
      title: 'Youth Leadership Forum',
      description: 'Inspiring the next generation of African leaders',
      date: 'June 5, 2025',
      time: '11:00 AM GMT',
      status: 'upcoming',
      duration: '3 hours',
      platforms: ['YouTube', 'Instagram']
    }
  ]

  const pastStreams = [
    {
      title: 'Royal Charity Gala Highlights',
      description: 'Highlights from the successful charity gala supporting education initiatives',
      date: 'December 10, 2024',
      views: '125K views',
      duration: '45 minutes',
      platforms: ['YouTube']
    },
    {
      title: 'Traditional Arts Exhibition Tour',
      description: 'Virtual tour of the royal collection of traditional Ghanaian arts',
      date: 'November 15, 2024',
      views: '89K views',
      duration: '1 hour',
      platforms: ['YouTube', 'Facebook']
    },
    {
      title: 'Royal Address on National Development',
      description: 'His Royal Majesty\'s address on Ghana\'s development initiatives',
      date: 'October 20, 2024',
      views: '200K views',
      duration: '30 minutes',
      platforms: ['All Platforms']
    }
  ]

  const streamingPlatforms = [
    { name: 'YouTube', icon: 'fab fa-youtube', color: 'text-red-500' },
    { name: 'Facebook', icon: 'fab fa-facebook', color: 'text-blue-500' },
    { name: 'Instagram', icon: 'fab fa-instagram', color: 'text-pink-500' },
    { name: 'LinkedIn', icon: 'fab fa-linkedin', color: 'text-blue-600' },
    { name: 'Royal Website', icon: 'fas fa-crown', color: 'text-royalGold' }
  ]

  return (
    <section className="py-20 relative overflow-hidden">
      {/* Background with gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-royalBlue via-royalBlue/95 to-royalBlue/90"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/15 via-transparent to-royalGold/5"></div>
      
      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-serif font-bold text-white mb-4">
            Royal Streaming
          </h2>
          <div className="w-32 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
          <p className="text-lg text-white/90 max-w-3xl mx-auto leading-relaxed">
            Watch royal events, ceremonies, and educational programs live from anywhere in the world.
          </p>
        </motion.div>

        {/* Live Stream Section */}
        {liveStreams.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="mb-16"
          >
            <h3 className="text-2xl font-serif font-bold text-white mb-8 flex items-center">
              <span className="w-3 h-3 bg-red-500 rounded-full mr-3 animate-pulse"></span>
              Live Now
            </h3>
            
            {liveStreams.map((stream, index) => (
              <div key={index} className="bg-white/10 backdrop-blur-lg rounded-3xl overflow-hidden border border-white/20 shadow-2xl">
                <div className="md:flex">
                  <div className="md:w-1/2 relative">
                    <div className="h-64 md:h-full bg-gradient-to-br from-red-500/20 to-royalGold/20 flex items-center justify-center relative">
                      <i className="fas fa-video text-8xl text-white/50"></i>
                      <div className="absolute top-4 left-4 px-3 py-1 bg-red-500 text-white text-sm font-bold rounded-full flex items-center">
                        <span className="w-2 h-2 bg-white rounded-full mr-2 animate-pulse"></span>
                        LIVE
                      </div>
                    </div>
                  </div>
                  <div className="md:w-1/2 p-8">
                    <h4 className="text-2xl font-bold text-white mb-4">{stream.title}</h4>
                    <p className="text-white/80 mb-6 leading-relaxed">{stream.description}</p>
                    
                    <div className="space-y-3 mb-6">
                      <div className="flex items-center text-white/70">
                        <i className="fas fa-calendar mr-3 text-royalGold"></i>
                        {stream.date} at {stream.time}
                      </div>
                      <div className="flex items-center text-white/70">
                        <i className="fas fa-users mr-3 text-royalGold"></i>
                        {stream.viewers}
                      </div>
                      <div className="flex items-center text-white/70">
                        <i className="fas fa-clock mr-3 text-royalGold"></i>
                        {stream.duration}
                      </div>
                    </div>
                    
                    <motion.button
                      className="w-full px-6 py-3 relative overflow-hidden text-royalBlue font-bold rounded-xl shadow-2xl border-2 border-yellow-300 group mb-4"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      style={{
                        background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 25%, #ffd700 50%, #b8860b 75%, #ffd700 100%)',
                        boxShadow: '0 12px 40px rgba(255, 215, 0, 0.5)'
                      }}
                    >
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1200 ease-out"></div>
                      <span className="relative z-10 font-extrabold">
                        <i className="fas fa-play mr-2"></i>
                        Watch Live Stream
                      </span>
                    </motion.button>
                    
                    <div className="flex flex-wrap gap-2">
                      {stream.platforms.map((platform, platformIndex) => (
                        <span key={platformIndex} className="px-3 py-1 bg-white/20 text-white text-xs rounded-full">
                          {platform}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </motion.div>
        )}

        {/* Upcoming Streams */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mb-16"
        >
          <h3 className="text-2xl font-serif font-bold text-white mb-8">Upcoming Streams</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {upcomingStreams.map((stream, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -10 }}
                className="bg-white/10 backdrop-blur-lg rounded-2xl overflow-hidden border border-white/20 shadow-xl"
              >
                <div className="h-48 bg-gradient-to-br from-royalGold/20 to-royalBlue/20 flex items-center justify-center relative">
                  <i className="fas fa-calendar-alt text-6xl text-royalGold"></i>
                  <div className="absolute top-4 right-4 px-2 py-1 bg-blue-500/80 text-white text-xs font-semibold rounded">
                    Upcoming
                  </div>
                </div>
                
                <div className="p-6">
                  <h4 className="text-lg font-bold text-white mb-3">{stream.title}</h4>
                  <p className="text-white/80 text-sm mb-4 leading-relaxed">{stream.description}</p>
                  
                  <div className="space-y-2 mb-4">
                    <div className="flex items-center text-white/70 text-sm">
                      <i className="fas fa-calendar mr-2 text-royalGold"></i>
                      {stream.date}
                    </div>
                    <div className="flex items-center text-white/70 text-sm">
                      <i className="fas fa-clock mr-2 text-royalGold"></i>
                      {stream.time} • {stream.duration}
                    </div>
                  </div>
                  
                  <motion.button
                    className="w-full px-4 py-2 bg-white/10 border border-white/20 text-white text-sm font-semibold rounded-lg hover:bg-white/20 transition-all duration-300"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    Set Reminder
                  </motion.button>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Past Streams */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mb-16"
        >
          <h3 className="text-2xl font-serif font-bold text-white mb-8">Watch Previous Streams</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {pastStreams.map((stream, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
                className="bg-white/5 backdrop-blur-lg rounded-2xl overflow-hidden border border-white/10 shadow-lg"
              >
                <div className="h-40 bg-gradient-to-br from-gray-500/20 to-gray-700/20 flex items-center justify-center relative">
                  <i className="fas fa-play-circle text-5xl text-white/60"></i>
                </div>
                
                <div className="p-6">
                  <h4 className="text-lg font-bold text-white/90 mb-3">{stream.title}</h4>
                  <p className="text-white/70 text-sm mb-4 leading-relaxed">{stream.description}</p>
                  
                  <div className="flex items-center justify-between text-white/60 text-sm mb-4">
                    <span>{stream.date}</span>
                    <span>{stream.views}</span>
                  </div>
                  
                  <motion.button
                    className="w-full px-4 py-2 bg-white/10 border border-white/20 text-white text-sm font-semibold rounded-lg hover:bg-white/20 transition-all duration-300"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <i className="fas fa-play mr-2"></i>
                    Watch Replay
                  </motion.button>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Streaming Platforms */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <h3 className="text-2xl font-serif font-bold text-white mb-8">Follow Us On</h3>
          <div className="flex flex-wrap justify-center gap-6">
            {streamingPlatforms.map((platform, index) => (
              <motion.a
                key={index}
                href="#"
                className="flex items-center px-6 py-3 bg-white/10 backdrop-blur-lg rounded-xl border border-white/20 hover:bg-white/20 transition-all duration-300"
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
              >
                <i className={`${platform.icon} text-2xl ${platform.color} mr-3`}></i>
                <span className="text-white font-semibold">{platform.name}</span>
              </motion.a>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default Streaming
