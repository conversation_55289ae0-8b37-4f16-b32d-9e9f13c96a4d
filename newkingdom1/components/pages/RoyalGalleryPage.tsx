'use client'

import { motion } from 'framer-motion'

const RoyalGalleryPage = () => {
  const galleryCategories = [
    {
      title: 'Royal Ceremonies',
      description: 'Historic moments and ceremonial events from the Royal Palace',
      images: [
        { title: 'Coronation Preparations', description: 'Behind the scenes of the upcoming coronation ceremony' },
        { title: 'Royal Investiture', description: 'Traditional ceremony honoring distinguished citizens' },
        { title: 'State Banquet', description: 'Formal dinner with international dignitaries' },
        { title: 'Royal Procession', description: 'Traditional procession through the capital' }
      ]
    },
    {
      title: 'Cultural Heritage',
      description: 'Celebrating Ghana\'s rich cultural traditions and customs',
      images: [
        { title: 'Kente Weaving Masters', description: 'Traditional artisans creating the iconic Kente cloth' },
        { title: 'Adinkra Symbols', description: 'Sacred symbols representing wisdom and knowledge' },
        { title: 'Traditional Dance', description: 'Cultural performances at royal celebrations' },
        { title: 'Royal Regalia', description: 'Historic crowns, scepters, and ceremonial items' }
      ]
    },
    {
      title: 'Palace Architecture',
      description: 'The magnificent architecture and grounds of the Royal Palace',
      images: [
        { title: 'Royal Palace Exterior', description: 'The majestic facade of the Royal Palace' },
        { title: 'Throne Room', description: 'The grand throne room where ceremonies take place' },
        { title: 'Royal Gardens', description: 'Beautifully landscaped gardens surrounding the palace' },
        { title: 'Royal Library', description: 'Historic library containing ancient texts and documents' }
      ]
    },
    {
      title: 'Community Engagement',
      description: 'Royal family involvement in community development and social initiatives',
      images: [
        { title: 'School Visits', description: 'Royal visits to local schools and educational institutions' },
        { title: 'Healthcare Initiatives', description: 'Supporting healthcare programs in rural communities' },
        { title: 'Youth Programs', description: 'Mentoring and leadership development for young people' },
        { title: 'Cultural Festivals', description: 'Participating in local cultural celebrations' }
      ]
    },
    {
      title: 'International Relations',
      description: 'Diplomatic meetings and international partnerships',
      images: [
        { title: 'Diplomatic Reception', description: 'Welcoming international ambassadors and dignitaries' },
        { title: 'Cultural Exchange', description: 'Promoting Ghanaian culture on the global stage' },
        { title: 'Business Summit', description: 'Meeting with international business leaders' },
        { title: 'UN Assembly', description: 'Representing Ghana at international forums' }
      ]
    },
    {
      title: 'Royal Family',
      description: 'Personal moments and family traditions of the Royal Family',
      images: [
        { title: 'Family Portrait', description: 'Official portrait of the Royal Family' },
        { title: 'Royal Wedding', description: 'Celebrating royal family marriages and unions' },
        { title: 'Coming of Age', description: 'Traditional ceremonies marking important milestones' },
        { title: 'Family Traditions', description: 'Private moments showcasing family customs' }
      ]
    }
  ]

  const featuredImage = {
    title: 'His Royal Majesty in Traditional Regalia',
    description: 'A magnificent portrait of His Royal Majesty wearing the traditional royal regalia, symbolizing the rich heritage and dignity of the Ghanaian monarchy.',
    category: 'Royal Portrait'
  }

  return (
    <section className="py-20 relative overflow-hidden">
      {/* Background with gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-royalBlue via-royalBlue/95 to-royalBlue/90"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/15 via-transparent to-royalGold/5"></div>
      
      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-serif font-bold text-white mb-4">
            Royal Gallery
          </h2>
          <div className="w-32 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
          <p className="text-lg text-white/90 max-w-3xl mx-auto leading-relaxed">
            Explore the visual heritage of the Kingdom through our comprehensive collection of photographs, artwork, and historical documentation.
          </p>
        </motion.div>

        {/* Featured Image */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mb-20"
        >
          <div className="bg-white/10 backdrop-blur-lg rounded-3xl overflow-hidden border border-white/20 shadow-2xl">
            <div className="md:flex">
              <div className="md:w-1/2">
                <div className="h-96 bg-gradient-to-br from-royalGold/30 to-royalBlue/30 flex items-center justify-center">
                  <i className="fas fa-crown text-8xl text-royalGold"></i>
                </div>
              </div>
              <div className="md:w-1/2 p-8 flex items-center">
                <div>
                  <span className="px-3 py-1 bg-royalGold/20 text-royalGold text-sm font-semibold rounded-full mb-4 inline-block">
                    {featuredImage.category}
                  </span>
                  <h3 className="text-2xl md:text-3xl font-serif font-bold text-white mb-4">
                    {featuredImage.title}
                  </h3>
                  <p className="text-white/80 leading-relaxed mb-6">
                    {featuredImage.description}
                  </p>
                  <motion.button
                    className="px-6 py-3 relative overflow-hidden text-royalBlue font-bold rounded-xl shadow-lg border border-yellow-300 group"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    style={{
                      background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 25%, #ffd700 50%, #b8860b 75%, #ffd700 100%)',
                      boxShadow: '0 8px 32px rgba(255, 215, 0, 0.4)'
                    }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></div>
                    <span className="relative z-10 font-extrabold">
                      <i className="fas fa-expand mr-2"></i>
                      View Full Size
                    </span>
                  </motion.button>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Gallery Categories */}
        <div className="space-y-16">
          {galleryCategories.map((category, categoryIndex) => (
            <motion.div
              key={categoryIndex}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: categoryIndex * 0.1 }}
              viewport={{ once: true }}
            >
              <div className="text-center mb-12">
                <h3 className="text-3xl font-serif font-bold text-white mb-4">{category.title}</h3>
                <p className="text-white/80 max-w-2xl mx-auto leading-relaxed">{category.description}</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {category.images.map((image, imageIndex) => (
                  <motion.div
                    key={imageIndex}
                    initial={{ opacity: 0, scale: 0.9 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.6, delay: imageIndex * 0.1 }}
                    viewport={{ once: true }}
                    whileHover={{ y: -10, scale: 1.02 }}
                    className="bg-white/10 backdrop-blur-lg rounded-2xl overflow-hidden border border-white/20 shadow-xl cursor-pointer group"
                  >
                    {/* Image Placeholder */}
                    <div className="h-48 bg-gradient-to-br from-royalGold/20 to-royalBlue/20 flex items-center justify-center relative overflow-hidden">
                      <i className="fas fa-image text-4xl text-royalGold group-hover:scale-110 transition-transform duration-300"></i>
                      <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                        <i className="fas fa-search-plus text-white text-2xl"></i>
                      </div>
                    </div>
                    
                    {/* Image Info */}
                    <div className="p-4">
                      <h4 className="text-white font-bold text-sm mb-2 leading-tight">{image.title}</h4>
                      <p className="text-white/70 text-xs leading-relaxed">{image.description}</p>
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* View More Button for each category */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                viewport={{ once: true }}
                className="text-center mt-8"
              >
                <motion.button
                  className="px-6 py-2 bg-white/10 border border-white/20 text-white font-semibold rounded-lg hover:bg-white/20 transition-all duration-300"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  View More {category.title} →
                </motion.button>
              </motion.div>
            </motion.div>
          ))}
        </div>

        {/* Gallery Stats */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mt-20"
        >
          <div className="bg-white/10 backdrop-blur-lg rounded-3xl p-8 border border-white/20 shadow-2xl">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
              <div>
                <div className="text-3xl font-bold text-royalGold mb-2">500+</div>
                <div className="text-white/80 text-sm">Royal Photographs</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-royalGold mb-2">50+</div>
                <div className="text-white/80 text-sm">Historic Events</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-royalGold mb-2">25+</div>
                <div className="text-white/80 text-sm">Years Documented</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-royalGold mb-2">100+</div>
                <div className="text-white/80 text-sm">Cultural Artifacts</div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Download Gallery */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <h3 className="text-2xl font-serif font-bold text-white mb-4">Download High-Resolution Images</h3>
          <p className="text-white/80 mb-8 max-w-2xl mx-auto">
            Access our complete collection of high-resolution royal photographs for media, educational, and research purposes.
          </p>
          <motion.button
            className="px-8 py-3 relative overflow-hidden text-royalBlue font-bold rounded-xl shadow-2xl border-2 border-yellow-300 group"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            style={{
              background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 25%, #ffd700 50%, #b8860b 75%, #ffd700 100%)',
              boxShadow: '0 12px 40px rgba(255, 215, 0, 0.5)'
            }}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1200 ease-out"></div>
            <span className="relative z-10 font-extrabold">
              <i className="fas fa-download mr-2"></i>
              Request Media Kit
            </span>
          </motion.button>
        </motion.div>
      </div>
    </section>
  )
}

export default RoyalGalleryPage
