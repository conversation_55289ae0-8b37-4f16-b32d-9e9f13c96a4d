'use client'

import React from 'react'
import { motion } from 'framer-motion'
import type { TicketTier } from '@/types/forms'

interface NFTBenefit {
  title: string
  description: string
  icon: string
}

interface PurchaseStep {
  step: string
  title: string
  description: string
}

const Tickets = () => {
  const ticketTiers: TicketTier[] = [
    {
      name: 'Royal Coronation NFT - VIP',
      price: '0.5 ETH',
      usdPrice: '$1,200',
      description: 'Exclusive VIP access to the Royal Coronation ceremony with premium seating and special privileges.',
      features: [
        'Front row seating at the coronation ceremony',
        'Meet & greet with His Royal Majesty',
        'Exclusive royal dinner invitation',
        'Commemorative royal gift package',
        'Professional photography session',
        'Lifetime access to royal events',
        'Limited edition NFT artwork',
        'Certificate of authenticity'
      ],
      limited: '50 Available',
      rarity: 'Ultra Rare',
      benefits: 'Lifetime Royal Access'
    },
    {
      name: 'Royal Coronation NFT - Premium',
      price: '0.25 ETH',
      usdPrice: '$600',
      description: 'Premium access to witness this historic moment with excellent viewing and exclusive amenities.',
      features: [
        'Premium seating at the ceremony',
        'Welcome reception access',
        'Royal lunch invitation',
        'Commemorative program book',
        'Digital photo collection',
        'Priority booking for future events',
        'Premium NFT artwork',
        'Digital certificate'
      ],
      limited: '200 Available',
      rarity: 'Rare',
      benefits: 'Premium Experience'
    },
    {
      name: 'Royal Coronation NFT - Standard',
      price: '0.1 ETH',
      usdPrice: '$240',
      description: 'Standard access to be part of this historic coronation ceremony and witness royal history.',
      features: [
        'General admission seating',
        'Ceremony program access',
        'Light refreshments',
        'Digital commemorative photo',
        'Event livestream access',
        'Standard NFT artwork',
        'Participation certificate',
        'Royal newsletter subscription'
      ],
      limited: '1000 Available',
      rarity: 'Common',
      benefits: 'Historic Participation'
    }
  ]

  const nftBenefits: NFTBenefit[] = [
    {
      title: 'Blockchain Verified',
      description: 'Each ticket is a unique NFT stored on the Ethereum blockchain',
      icon: 'fas fa-shield-alt'
    },
    {
      title: 'Transferable',
      description: 'Tickets can be transferred or gifted to other wallet addresses',
      icon: 'fas fa-exchange-alt'
    },
    {
      title: 'Collectible Value',
      description: 'Historic NFTs that may appreciate in value over time',
      icon: 'fas fa-gem'
    },
    {
      title: 'Proof of Attendance',
      description: 'Permanent digital proof of your participation in royal history',
      icon: 'fas fa-certificate'
    }
  ]

  const purchaseSteps: PurchaseStep[] = [
    {
      step: '1',
      title: 'Connect Wallet',
      description: 'Connect your MetaMask or compatible Web3 wallet'
    },
    {
      step: '2',
      title: 'Select Tier',
      description: 'Choose your preferred ticket tier and quantity'
    },
    {
      step: '3',
      title: 'Complete Purchase',
      description: 'Confirm transaction and receive your NFT ticket'
    },
    {
      step: '4',
      title: 'Attend Event',
      description: 'Present your NFT for entry to the coronation ceremony'
    }
  ]

  return (
    <section className="py-20 relative overflow-hidden">
      {/* Background with gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-royalBlue via-royalBlue/95 to-royalBlue/90"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/15 via-transparent to-royalGold/5"></div>
      
      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-serif font-bold text-white mb-4">
            Royal Coronation NFT Tickets
          </h2>
          <div className="w-32 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
          <p className="text-lg text-white/90 max-w-3xl mx-auto leading-relaxed">
            Secure your place in history with exclusive NFT tickets to the Royal Coronation ceremony. Each ticket is a unique digital collectible that grants access to this once-in-a-lifetime event.
          </p>
        </motion.div>

        {/* Event Details */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="bg-white/10 backdrop-blur-lg rounded-3xl p-8 border border-white/20 shadow-2xl mb-16"
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div>
              <i className="fas fa-calendar-alt text-4xl text-royalGold mb-4"></i>
              <h3 className="text-xl font-bold text-white mb-2">Date</h3>
              <p className="text-white/80">March 15, 2025</p>
              <p className="text-white/60 text-sm">10:00 AM GMT</p>
            </div>
            <div>
              <i className="fas fa-map-marker-alt text-4xl text-royalGold mb-4"></i>
              <h3 className="text-xl font-bold text-white mb-2">Location</h3>
              <p className="text-white/80">Royal Palace</p>
              <p className="text-white/60 text-sm">Accra, Ghana</p>
            </div>
            <div>
              <i className="fas fa-users text-4xl text-royalGold mb-4"></i>
              <h3 className="text-xl font-bold text-white mb-2">Capacity</h3>
              <p className="text-white/80">1,250 Attendees</p>
              <p className="text-white/60 text-sm">Limited NFT Collection</p>
            </div>
          </div>
        </motion.div>

        {/* NFT Benefits */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16"
        >
          {nftBenefits.map((benefit, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 text-center border border-white/20 shadow-xl"
            >
              <i className={`${benefit.icon} text-3xl text-royalGold mb-4`}></i>
              <h3 className="text-lg font-bold text-white mb-2">{benefit.title}</h3>
              <p className="text-white/80 text-sm leading-relaxed">{benefit.description}</p>
            </motion.div>
          ))}
        </motion.div>

        {/* Ticket Tiers */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16"
        >
          {ticketTiers.map((tier, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -10 }}
              className={`bg-white/10 backdrop-blur-lg rounded-3xl overflow-hidden border border-white/20 shadow-2xl ${
                index === 0 ? 'ring-2 ring-royalGold' : ''
              }`}
            >
              {index === 0 && (
                <div className="bg-gradient-to-r from-royalGold to-yellow-500 text-royalBlue text-center py-2 font-bold text-sm">
                  MOST EXCLUSIVE
                </div>
              )}
              
              <div className="p-8">
                <div className="text-center mb-6">
                  <h3 className="text-2xl font-bold text-white mb-2">{tier.name}</h3>
                  <div className="text-4xl font-bold text-royalGold mb-1">{tier.price}</div>
                  <div className="text-white/60 text-sm">{tier.usdPrice} USD</div>
                </div>
                
                <div className="flex justify-center gap-4 mb-6">
                  <span className="px-3 py-1 bg-royalGold/20 text-royalGold text-xs font-semibold rounded-full">
                    {tier.rarity}
                  </span>
                  <span className="px-3 py-1 bg-blue-500/20 text-blue-400 text-xs font-semibold rounded-full">
                    {tier.limited}
                  </span>
                </div>
                
                <p className="text-white/80 text-sm mb-6 leading-relaxed text-center">
                  {tier.description}
                </p>
                
                <div className="space-y-3 mb-8">
                  {tier.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-center text-white/80 text-sm">
                      <i className="fas fa-check text-royalGold mr-3"></i>
                      {feature}
                    </div>
                  ))}
                </div>
                
                <motion.button
                  className="w-full py-3 relative overflow-hidden text-royalBlue font-bold rounded-xl shadow-2xl border-2 border-yellow-300 group"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  style={{
                    background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 25%, #ffd700 50%, #b8860b 75%, #ffd700 100%)',
                    boxShadow: '0 12px 40px rgba(255, 215, 0, 0.5)'
                  }}
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1200 ease-out"></div>
                  <span className="relative z-10 font-extrabold">
                    <i className="fas fa-shopping-cart mr-2"></i>
                    Purchase NFT Ticket
                  </span>
                </motion.button>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* How to Purchase */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mb-16"
        >
          <h3 className="text-3xl font-serif font-bold text-white text-center mb-12">How to Purchase</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {purchaseSteps.map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-royalGold to-yellow-500 rounded-full flex items-center justify-center text-royalBlue font-bold text-xl">
                  {step.step}
                </div>
                <h4 className="text-lg font-bold text-white mb-2">{step.title}</h4>
                <p className="text-white/80 text-sm leading-relaxed">{step.description}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Connect Wallet CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <div className="bg-white/10 backdrop-blur-lg rounded-3xl p-12 border border-white/20 shadow-2xl max-w-2xl mx-auto">
            <h3 className="text-2xl font-serif font-bold text-white mb-4">Ready to Make History?</h3>
            <p className="text-white/80 mb-8 leading-relaxed">
              Connect your Web3 wallet to purchase your exclusive Royal Coronation NFT ticket and secure your place at this historic event.
            </p>
            <motion.button
              className="px-12 py-4 relative overflow-hidden text-royalBlue font-bold text-lg rounded-xl shadow-2xl border-2 border-yellow-300 group"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              style={{
                background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 25%, #ffd700 50%, #b8860b 75%, #ffd700 100%)',
                boxShadow: '0 12px 40px rgba(255, 215, 0, 0.5)'
              }}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1200 ease-out"></div>
              <span className="relative z-10 font-extrabold">
                <i className="fas fa-wallet mr-2"></i>
                Connect Wallet & Purchase
              </span>
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default Tickets
