'use client'

import { motion } from 'framer-motion'

const StrategicPartnersPage = () => {
  const partnerCategories = [
    {
      title: 'Financial Partners',
      description: 'Strategic financial institutions supporting economic growth and development',
      partners: [
        {
          name: 'Remit Global',
          description: 'Leading financial services provider supporting cross-border transactions and economic development.',
          logo: 'fas fa-university',
          website: '#',
          partnership: 'Financial Services & Development'
        },
        {
          name: 'Ghana Commercial Bank',
          description: 'Premier banking partner facilitating royal treasury and development projects.',
          logo: 'fas fa-piggy-bank',
          website: '#',
          partnership: 'Banking & Treasury Services'
        },
        {
          name: 'African Development Bank',
          description: 'Continental development finance institution supporting infrastructure projects.',
          logo: 'fas fa-building',
          website: '#',
          partnership: 'Infrastructure Development'
        }
      ]
    },
    {
      title: 'Cultural & Heritage Partners',
      description: 'Organizations dedicated to preserving and promoting Ghanaian culture',
      partners: [
        {
          name: 'Royal Lion Heritage',
          description: 'Cultural preservation organization maintaining traditional royal customs and ceremonies.',
          logo: 'fas fa-crown',
          website: '#',
          partnership: 'Cultural Preservation'
        },
        {
          name: 'UNESCO Ghana',
          description: 'United Nations partner for cultural heritage preservation and education.',
          logo: 'fas fa-globe-africa',
          website: '#',
          partnership: 'Heritage Conservation'
        },
        {
          name: 'National Museum of Ghana',
          description: 'Premier cultural institution showcasing Ghana\'s rich historical heritage.',
          logo: 'fas fa-landmark',
          website: '#',
          partnership: 'Cultural Exhibition'
        }
      ]
    },
    {
      title: 'Educational Partners',
      description: 'Leading educational institutions advancing knowledge and skills development',
      partners: [
        {
          name: 'TEF (Tony Elumelu Foundation)',
          description: 'Entrepreneurship foundation empowering African youth through education and mentorship.',
          logo: 'fas fa-graduation-cap',
          website: '#',
          partnership: 'Youth Entrepreneurship'
        },
        {
          name: 'University of Ghana',
          description: 'Premier academic institution providing research and educational excellence.',
          logo: 'fas fa-university',
          website: '#',
          partnership: 'Academic Research'
        },
        {
          name: 'KNUST',
          description: 'Leading technology university advancing innovation and technical education.',
          logo: 'fas fa-cogs',
          website: '#',
          partnership: 'Technology Innovation'
        }
      ]
    },
    {
      title: 'Technology Partners',
      description: 'Innovation-driven companies supporting digital transformation',
      partners: [
        {
          name: 'Lightace Global',
          description: 'Technology solutions provider bringing digital innovation to traditional governance.',
          logo: 'fas fa-lightbulb',
          website: '#',
          partnership: 'Digital Transformation'
        },
        {
          name: 'MTN Ghana',
          description: 'Leading telecommunications provider enabling digital connectivity across the kingdom.',
          logo: 'fas fa-signal',
          website: '#',
          partnership: 'Telecommunications'
        },
        {
          name: 'Google for Africa',
          description: 'Technology giant supporting digital literacy and innovation initiatives.',
          logo: 'fab fa-google',
          website: '#',
          partnership: 'Digital Innovation'
        }
      ]
    },
    {
      title: 'Governance Partners',
      description: 'Traditional and modern governance institutions supporting royal administration',
      partners: [
        {
          name: 'Akuapem Nifaman Council',
          description: 'Traditional governance council supporting the kingdom\'s leadership structure and customs.',
          logo: 'fas fa-shield-alt',
          website: '#',
          partnership: 'Traditional Governance'
        },
        {
          name: 'National House of Chiefs',
          description: 'Constitutional body representing traditional authorities in Ghana.',
          logo: 'fas fa-balance-scale',
          website: '#',
          partnership: 'Constitutional Governance'
        },
        {
          name: 'Ministry of Chieftaincy',
          description: 'Government ministry overseeing traditional authority affairs.',
          logo: 'fas fa-government',
          website: '#',
          partnership: 'Government Relations'
        }
      ]
    }
  ]

  const partnershipBenefits = [
    {
      title: 'Strategic Collaboration',
      description: 'Access to exclusive partnerships and collaborative opportunities',
      icon: 'fas fa-handshake',
      color: 'from-royalGold to-yellow-500'
    },
    {
      title: 'Global Network',
      description: 'Connect with international partners and expand your reach',
      icon: 'fas fa-globe',
      color: 'from-royalBlue to-blue-600'
    },
    {
      title: 'Cultural Impact',
      description: 'Contribute to preserving and promoting African heritage',
      icon: 'fas fa-heart',
      color: 'from-red-500 to-pink-500'
    },
    {
      title: 'Economic Growth',
      description: 'Participate in sustainable development and economic initiatives',
      icon: 'fas fa-chart-line',
      color: 'from-green-500 to-emerald-500'
    }
  ]

  return (
    <section className="py-20 relative overflow-hidden">
      {/* Background with gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-cream via-ivory to-cream"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/8 via-transparent to-royalBlue/8"></div>
      
      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-serif font-bold bg-gradient-to-r from-royalBlue to-royalBlue/80 bg-clip-text text-transparent mb-4">
            Strategic Partners of the Crown
          </h2>
          <div className="w-32 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
          <p className="text-lg text-gray-700 max-w-4xl mx-auto leading-relaxed">
            Our distinguished network of partners represents the finest institutions and organizations committed to advancing Ghana's prosperity, cultural heritage, and global impact.
          </p>
        </motion.div>

        {/* Partnership Benefits */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16"
        >
          {partnershipBenefits.map((benefit, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -5 }}
              className="bg-white rounded-2xl p-6 shadow-xl border border-gray-200 text-center"
            >
              <div className={`w-16 h-16 mx-auto mb-4 bg-gradient-to-br ${benefit.color} rounded-2xl flex items-center justify-center`}>
                <i className={`${benefit.icon} text-white text-2xl`}></i>
              </div>
              <h3 className="text-lg font-bold text-royalBlue mb-2">{benefit.title}</h3>
              <p className="text-gray-600 text-sm leading-relaxed">{benefit.description}</p>
            </motion.div>
          ))}
        </motion.div>

        {/* Partner Categories */}
        <div className="space-y-16">
          {partnerCategories.map((category, categoryIndex) => (
            <motion.div
              key={categoryIndex}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: categoryIndex * 0.1 }}
              viewport={{ once: true }}
            >
              <div className="text-center mb-12">
                <h3 className="text-3xl font-serif font-bold text-royalBlue mb-4">{category.title}</h3>
                <p className="text-gray-600 max-w-2xl mx-auto leading-relaxed">{category.description}</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {category.partners.map((partner, partnerIndex) => (
                  <motion.div
                    key={partnerIndex}
                    initial={{ opacity: 0, scale: 0.9 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.6, delay: partnerIndex * 0.1 }}
                    viewport={{ once: true }}
                    whileHover={{ y: -10 }}
                    className="bg-white rounded-2xl p-6 shadow-xl border border-gray-200 hover:shadow-2xl transition-all duration-300"
                  >
                    {/* Logo */}
                    <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-royalGold/20 to-royalBlue/20 rounded-full flex items-center justify-center border-4 border-royalGold/30">
                      <i className={`${partner.logo} text-royalBlue text-2xl`}></i>
                    </div>
                    
                    {/* Partner Info */}
                    <h4 className="text-xl font-bold text-royalBlue mb-2 text-center">{partner.name}</h4>
                    <p className="text-gray-600 text-sm mb-4 leading-relaxed text-center">{partner.description}</p>
                    
                    {/* Partnership Type */}
                    <div className="text-center mb-4">
                      <span className="px-3 py-1 bg-royalGold/20 text-royalGold text-xs font-semibold rounded-full">
                        {partner.partnership}
                      </span>
                    </div>
                    
                    {/* Visit Website Button */}
                    <motion.a
                      href={partner.website}
                      className="block w-full px-4 py-2 relative overflow-hidden text-royalBlue font-bold text-sm rounded-lg shadow-lg border border-yellow-300 group text-center"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      style={{
                        background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 25%, #ffd700 50%, #b8860b 75%, #ffd700 100%)',
                        boxShadow: '0 4px 16px rgba(255, 215, 0, 0.3)'
                      }}
                    >
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></div>
                      <span className="relative z-10 font-extrabold">Visit Website →</span>
                    </motion.a>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          ))}
        </div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mt-20"
        >
          <div className="bg-white rounded-3xl p-12 shadow-2xl border border-royalGold/20 max-w-4xl mx-auto">
            <h3 className="text-3xl font-serif font-bold text-royalBlue mb-6">Become a Strategic Partner</h3>
            <p className="text-gray-700 mb-8 leading-relaxed max-w-2xl mx-auto">
              Join our distinguished network of partners and contribute to shaping Africa's prosperous future through meaningful collaboration and shared vision.
            </p>
            <motion.button
              className="px-12 py-4 relative overflow-hidden text-royalBlue font-bold text-lg rounded-xl shadow-2xl border-2 border-yellow-300 group"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              style={{
                background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 25%, #ffd700 50%, #b8860b 75%, #ffd700 100%)',
                boxShadow: '0 12px 40px rgba(255, 215, 0, 0.5)'
              }}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1200 ease-out"></div>
              <span className="relative z-10 font-extrabold">
                <i className="fas fa-handshake mr-2"></i>
                Apply for Partnership
              </span>
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default StrategicPartnersPage
