'use client'

import { motion } from 'framer-motion'

const News = () => {
  const featuredNews = {
    title: 'Historic Coronation Preparations Underway',
    excerpt: 'The Royal Palace announces final preparations for the upcoming coronation ceremony, marking a new chapter in Ghana\'s royal heritage.',
    date: 'January 15, 2025',
    category: 'Royal Announcement',
    readTime: '5 min read',
    image: '/api/placeholder/600/400'
  }

  const newsArticles = [
    {
      title: 'Royal Initiative Launches Education Program',
      excerpt: 'A new educational initiative aims to provide scholarships and resources to underprivileged students across Ghana.',
      date: 'January 12, 2025',
      category: 'Education',
      readTime: '3 min read',
      image: '/api/placeholder/400/250'
    },
    {
      title: 'Cultural Heritage Preservation Project Announced',
      excerpt: 'The Kingdom announces a major project to preserve and digitize traditional Ghanaian cultural artifacts.',
      date: 'January 10, 2025',
      category: 'Culture',
      readTime: '4 min read',
      image: '/api/placeholder/400/250'
    },
    {
      title: 'Strategic Partnership with International Organizations',
      excerpt: 'New partnerships established to promote economic development and cultural exchange programs.',
      date: 'January 8, 2025',
      category: 'Partnerships',
      readTime: '6 min read',
      image: '/api/placeholder/400/250'
    },
    {
      title: 'Royal Family Hosts Charity Gala Success',
      excerpt: 'The recent charity gala raised significant funds for healthcare initiatives in rural communities.',
      date: 'January 5, 2025',
      category: 'Charity',
      readTime: '3 min read',
      image: '/api/placeholder/400/250'
    },
    {
      title: 'Youth Leadership Program Expansion',
      excerpt: 'The successful youth leadership program expands to include more regions and participants.',
      date: 'January 3, 2025',
      category: 'Youth',
      readTime: '4 min read',
      image: '/api/placeholder/400/250'
    },
    {
      title: 'Sustainable Development Goals Progress',
      excerpt: 'The Kingdom reports significant progress on environmental and sustainability initiatives.',
      date: 'December 30, 2024',
      category: 'Environment',
      readTime: '5 min read',
      image: '/api/placeholder/400/250'
    }
  ]

  const categories = ['All', 'Royal Announcement', 'Education', 'Culture', 'Partnerships', 'Charity', 'Youth', 'Environment']

  return (
    <section className="py-20 relative overflow-hidden">
      {/* Background with gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-cream via-ivory to-cream"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/8 via-transparent to-royalBlue/8"></div>
      
      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-serif font-bold bg-gradient-to-r from-royalBlue to-royalBlue/80 bg-clip-text text-transparent mb-4">
            Royal News & Updates
          </h2>
          <div className="w-32 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto leading-relaxed">
            Stay informed with the latest news, announcements, and updates from the Royal Palace and Kingdom initiatives.
          </p>
        </motion.div>

        {/* Featured News */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mb-16"
        >
          <div className="bg-white rounded-3xl shadow-2xl overflow-hidden border border-royalGold/20">
            <div className="md:flex">
              <div className="md:w-1/2">
                <div className="h-64 md:h-full bg-gradient-to-br from-royalGold/20 to-royalBlue/20 flex items-center justify-center">
                  <i className="fas fa-newspaper text-8xl text-royalGold"></i>
                </div>
              </div>
              <div className="md:w-1/2 p-8">
                <div className="flex items-center mb-4">
                  <span className="px-3 py-1 bg-royalGold/20 text-royalGold text-sm font-semibold rounded-full mr-3">
                    {featuredNews.category}
                  </span>
                  <span className="text-gray-500 text-sm">{featuredNews.date}</span>
                </div>
                
                <h3 className="text-2xl md:text-3xl font-serif font-bold text-royalBlue mb-4">
                  {featuredNews.title}
                </h3>
                
                <p className="text-gray-700 leading-relaxed mb-6">
                  {featuredNews.excerpt}
                </p>
                
                <div className="flex items-center justify-between">
                  <span className="text-gray-500 text-sm">{featuredNews.readTime}</span>
                  <motion.button
                    className="px-6 py-2 relative overflow-hidden text-royalBlue font-bold rounded-lg shadow-lg border border-yellow-300 group"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    style={{
                      background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 25%, #ffd700 50%, #b8860b 75%, #ffd700 100%)',
                      boxShadow: '0 4px 16px rgba(255, 215, 0, 0.3)'
                    }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></div>
                    <span className="relative z-10">Read Full Article</span>
                  </motion.button>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Category Filter */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="flex flex-wrap justify-center gap-3 mb-12"
        >
          {categories.map((category, index) => (
            <motion.button
              key={category}
              className="px-4 py-2 bg-white/80 backdrop-blur-sm border border-royalGold/30 text-royalBlue font-medium rounded-full hover:bg-royalGold/20 hover:border-royalGold transition-all duration-300"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {category}
            </motion.button>
          ))}
        </motion.div>

        {/* News Grid */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {newsArticles.map((article, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -10 }}
              className="bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-200 hover:shadow-2xl transition-all duration-300"
            >
              <div className="h-48 bg-gradient-to-br from-royalGold/10 to-royalBlue/10 flex items-center justify-center">
                <i className="fas fa-file-alt text-5xl text-royalGold"></i>
              </div>
              
              <div className="p-6">
                <div className="flex items-center justify-between mb-3">
                  <span className="px-3 py-1 bg-royalGold/10 text-royalGold text-xs font-semibold rounded-full">
                    {article.category}
                  </span>
                  <span className="text-gray-400 text-xs">{article.readTime}</span>
                </div>
                
                <h4 className="text-lg font-bold text-royalBlue mb-3 leading-tight">
                  {article.title}
                </h4>
                
                <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                  {article.excerpt}
                </p>
                
                <div className="flex items-center justify-between">
                  <span className="text-gray-400 text-xs">{article.date}</span>
                  <motion.button
                    className="text-royalGold hover:text-royalBlue font-semibold text-sm transition-colors duration-300"
                    whileHover={{ scale: 1.05 }}
                  >
                    Read More →
                  </motion.button>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Load More Button */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mt-12"
        >
          <motion.button
            className="px-8 py-3 relative overflow-hidden text-royalBlue font-bold rounded-xl shadow-lg border-2 border-yellow-300 group"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            style={{
              background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 25%, #ffd700 50%, #b8860b 75%, #ffd700 100%)',
              boxShadow: '0 8px 32px rgba(255, 215, 0, 0.4)'
            }}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></div>
            <span className="relative z-10 font-extrabold">Load More Articles</span>
          </motion.button>
        </motion.div>
      </div>
    </section>
  )
}

export default News
