'use client'

import { motion } from 'framer-motion'

const RoyalHighlights = () => {
  const highlights = [
    {
      icon: 'fas fa-calendar-alt',
      title: 'Upcoming Event',
      subtitle: 'Coronation Ceremony',
      description: 'Join us for the historic coronation of His Royal Majesty in 2025. A once-in-a-lifetime celebration of heritage and tradition.',
      location: 'Accra International Conference Centre',
      date: 'April 15, 2025 • 10:00 AM',
      buttonText: 'Learn More',
      buttonLink: '#coronation',
      gradient: 'from-blue-500 to-royalBlue'
    },
    {
      icon: 'fas fa-quote-right',
      title: 'Royal Address',
      subtitle: 'Message from His Majesty',
      description: '"Together, we will honor our ancestors while building a prosperous future for all Ghanaians. Our heritage guides us as we embrace innovation and progress."',
      author: 'His Royal Majesty',
      authorTitle: 'King of Ghana',
      buttonText: 'Read Full Message',
      buttonLink: '#message',
      gradient: 'from-royalGold to-yellow-500'
    },
    {
      icon: 'fas fa-lightbulb',
      title: 'Royal Initiative',
      subtitle: 'Ghana Heritage Preservation',
      description: 'A landmark project to document, preserve, and celebrate Ghana\'s rich cultural heritage for future generations.',
      progress: 70,
      target: 'December 2025',
      buttonText: 'View Project',
      buttonLink: '#projects',
      gradient: 'from-green-500 to-forestGreen'
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  }

  const cardVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  }

  return (
    <section id="highlights" className="bg-ivory py-16 royal-pattern">
      <div className="container mx-auto px-4">
        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl md:text-4xl font-serif font-bold text-royalBlue mb-4">Royal Highlights</h2>
          <div className="w-24 h-1 bg-royalGold mx-auto mb-4"></div>
          <p className="text-gray-600 max-w-2xl mx-auto">Discover the latest news, initiatives, and messages from the royal court.</p>
        </motion.div>

        <motion.div 
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-3 gap-8"
        >
          {highlights.map((highlight, index) => (
            <motion.div
              key={index}
              variants={cardVariants}
              whileHover={{ y: -10, transition: { duration: 0.3 } }}
              className="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100"
            >
              {/* Header */}
              <div className={`bg-gradient-to-r ${highlight.gradient} text-white p-6`}>
                <div className="flex items-center justify-between">
                  <h3 className="font-serif font-bold text-lg">{highlight.title}</h3>
                  <span className="text-2xl opacity-80">
                    <i className={highlight.icon}></i>
                  </span>
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                <h4 className="text-xl font-bold mb-3 text-royalBlue">{highlight.subtitle}</h4>
                
                {highlight.description && (
                  <p className="text-gray-700 mb-4 leading-relaxed">
                    {highlight.description}
                  </p>
                )}

                {/* Event Details */}
                {highlight.location && (
                  <div className="flex items-center text-sm text-gray-600 mb-3">
                    <i className="fas fa-map-marker-alt text-royalGold mr-2"></i>
                    <span>{highlight.location}</span>
                  </div>
                )}

                {highlight.date && (
                  <div className="flex items-center text-sm text-gray-600 mb-6">
                    <i className="fas fa-clock text-royalGold mr-2"></i>
                    <span>{highlight.date}</span>
                  </div>
                )}

                {/* Author Info */}
                {highlight.author && (
                  <div className="flex items-center mb-6">
                    <div className="w-12 h-12 rounded-full bg-royalBlue/10 flex items-center justify-center text-royalGold text-xl mr-3">
                      <i className="fas fa-crown"></i>
                    </div>
                    <div>
                      <p className="font-bold text-royalBlue">{highlight.author}</p>
                      <p className="text-sm text-gray-600">{highlight.authorTitle}</p>
                    </div>
                  </div>
                )}

                {/* Progress Bar */}
                {highlight.progress && (
                  <div className="mb-6">
                    <div className="mb-2 bg-gray-200 rounded-full h-3 overflow-hidden">
                      <motion.div 
                        className="bg-royalGold h-full rounded-full"
                        initial={{ width: 0 }}
                        whileInView={{ width: `${highlight.progress}%` }}
                        transition={{ duration: 1.5, delay: 0.5 }}
                        viewport={{ once: true }}
                      ></motion.div>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Progress: {highlight.progress}%</span>
                      <span className="text-royalBlue font-medium">Target: {highlight.target}</span>
                    </div>
                  </div>
                )}

                {/* Button */}
                <motion.a 
                  href={highlight.buttonLink}
                  className="block text-center py-3 px-6 bg-royalBlue text-white rounded-lg hover:bg-royalBlue/90 transition-colors duration-300 font-medium"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  {highlight.buttonText}
                </motion.a>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  )
}

export default RoyalHighlights
