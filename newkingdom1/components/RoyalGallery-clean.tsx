'use client'

import React, { useState } from 'react'
import type { GalleryItem } from '@/types'

const RoyalGallery = () => {
  const [selectedCategory, setSelectedCategory] = useState('All')

  const galleryItems: GalleryItem[] = [
    {
      id: 1,
      title: 'Royal Palace Exterior',
      category: 'Architecture',
      image: '/images/gallery/palace-exterior.jpg',
      description: 'The magnificent exterior of the Adukrom Royal Palace showcasing traditional Ghanaian architecture.'
    },
    {
      id: 2,
      title: 'Traditional Ceremony',
      category: 'Ceremonies',
      image: '/images/gallery/ceremony.jpg',
      description: 'A sacred traditional ceremony performed by the royal court.'
    },
    {
      id: 3,
      title: 'Royal Regalia',
      category: 'Artifacts',
      image: '/images/gallery/regalia.jpg',
      description: 'Ancient royal regalia passed down through generations.'
    },
    {
      id: 4,
      title: 'Cultural Festival',
      category: 'Events',
      image: '/images/gallery/festival.jpg',
      description: 'Annual cultural festival celebrating Adukrom heritage.'
    },
    {
      id: 5,
      title: 'Throne Room',
      category: 'Architecture',
      image: '/images/gallery/throne-room.jpg',
      description: 'The ornate throne room where royal audiences are held.'
    },
    {
      id: 6,
      title: 'Royal Gardens',
      category: 'Architecture',
      image: '/images/gallery/gardens.jpg',
      description: 'Beautiful royal gardens surrounding the palace grounds.'
    },
    {
      id: 7,
      title: 'Community Outreach',
      category: 'Events',
      image: '/images/gallery/outreach.jpg',
      description: 'Royal family engaging with local communities.'
    },
    {
      id: 8,
      title: 'Ancient Artifacts',
      category: 'Artifacts',
      image: '/images/gallery/artifacts.jpg',
      description: 'Collection of ancient artifacts from the kingdom\'s history.'
    }
  ]

  const categories = ['All', 'Architecture', 'Ceremonies', 'Artifacts', 'Events']

  const filteredItems = selectedCategory === 'All' 
    ? galleryItems 
    : galleryItems.filter(item => item.category === selectedCategory)

  return (
    <section id="gallery" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-serif font-bold text-royalBlue mb-4">Royal Gallery</h2>
          <div className="w-24 h-1 bg-royalGold mx-auto mb-8"></div>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto leading-relaxed">
            Explore the rich visual heritage of the Adukrom Kingdom through our curated collection 
            of photographs, artifacts, and memorable moments.
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`px-6 py-2 rounded-full font-medium transition-all duration-300 ${
                selectedCategory === category
                  ? 'bg-royalBlue text-white shadow-lg'
                  : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-200'
              }`}
            >
              {category}
            </button>
          ))}
        </div>

        {/* Gallery Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredItems.map((item) => (
            <div
              key={item.id}
              className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 hover:-translate-y-2 group"
            >
              {/* Image Container */}
              <div className="relative h-48 bg-gray-200 overflow-hidden">
                <div className="w-full h-full bg-gradient-to-br from-royalBlue/20 to-royalGold/20 flex items-center justify-center">
                  <div className="text-center">
                    <i className="fas fa-image text-4xl text-gray-400 mb-2"></i>
                    <p className="text-gray-500 text-sm">{item.title}</p>
                  </div>
                </div>
                
                {/* Overlay */}
                <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                  <button className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-colors">
                    <i className="fas fa-expand-alt"></i>
                  </button>
                </div>

                {/* Category Badge */}
                <div className="absolute top-3 left-3">
                  <span className="px-2 py-1 bg-royalBlue/80 text-white text-xs font-semibold rounded-full">
                    {item.category}
                  </span>
                </div>
              </div>

              {/* Content */}
              <div className="p-4">
                <h3 className="text-lg font-bold text-royalBlue mb-2">{item.title}</h3>
                <p className="text-gray-600 text-sm leading-relaxed">{item.description}</p>
              </div>
            </div>
          ))}
        </div>

        {/* Gallery Stats */}
        <div className="mt-16 bg-white rounded-2xl shadow-lg p-8">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-serif font-bold text-royalBlue mb-4">Gallery Collection</h3>
            <p className="text-gray-700 leading-relaxed">
              Our growing collection preserves the visual history and cultural heritage of the Adukrom Kingdom.
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-3xl font-bold text-royalBlue mb-2">500+</div>
              <p className="text-gray-600">Historical Photos</p>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-royalBlue mb-2">100+</div>
              <p className="text-gray-600">Artifacts Documented</p>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-royalBlue mb-2">50+</div>
              <p className="text-gray-600">Ceremonies Recorded</p>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-royalBlue mb-2">25+</div>
              <p className="text-gray-600">Years of History</p>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-royalBlue to-royalBlue/90 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-serif font-bold mb-4">Explore More</h3>
            <p className="text-white/90 mb-6 max-w-2xl mx-auto leading-relaxed">
              Visit our complete gallery to discover more treasures from the Adukrom Kingdom's 
              rich history and ongoing cultural celebrations.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="px-6 py-3 bg-royalGold text-royalBlue font-bold rounded-lg hover:bg-royalGold/90 transition-colors">
                <i className="fas fa-images mr-2"></i>
                View Full Gallery
              </button>
              <button className="px-6 py-3 bg-white/10 text-white font-bold rounded-lg hover:bg-white/20 transition-colors border border-white/30">
                <i className="fas fa-download mr-2"></i>
                Download Catalog
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default RoyalGallery
