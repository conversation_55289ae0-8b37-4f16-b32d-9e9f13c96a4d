'use client'

import React, { useState } from 'react'
import type { ContactFormData } from '@/types/forms'
import type { ContactInfo } from '@/types'

const Contact = () => {
  const [formData, setFormData] = useState<ContactFormData>({
    fullName: '',
    email: '',
    subject: '',
    message: ''
  })

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitMessage, setSubmitMessage] = useState('')

  const contactInfo: ContactInfo[] = [
    {
      icon: 'fas fa-map-marker-alt',
      title: 'Location',
      details: ['Adukrom, Ghana', 'West Africa'],
      gradient: 'from-blue-500 to-purple-600'
    },
    {
      icon: 'fas fa-phone',
      title: 'Phone',
      details: ['+233 XX XXX XXXX', 'Mon - Fri, 9AM - 5PM'],
      gradient: 'from-green-500 to-blue-500'
    },
    {
      icon: 'fas fa-envelope',
      title: 'Email',
      details: ['<EMAIL>', '<EMAIL>'],
      gradient: 'from-purple-500 to-pink-500'
    }
  ]

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    // Simulate form submission
    setTimeout(() => {
      setIsSubmitting(false)
      setSubmitMessage('Thank you for your message! We will get back to you soon.')
      setFormData({
        fullName: '',
        email: '',
        subject: '',
        message: ''
      })
    }, 2000)
  }

  return (
    <section id="contact" className="py-20 bg-royalBlue">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-serif font-bold text-white mb-4">Contact Us</h2>
          <div className="w-24 h-1 bg-royalGold mx-auto mb-8"></div>
          <p className="text-lg text-white/90 max-w-3xl mx-auto leading-relaxed">
            We welcome your inquiries and look forward to connecting with you. 
            Reach out to us for any questions about our kingdom, events, or initiatives.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
          {contactInfo.map((info, index) => (
            <div
              key={index}
              className="bg-white/10 backdrop-blur-lg rounded-xl p-6 text-center border border-white/20"
            >
              <div className="w-16 h-16 mx-auto mb-4 bg-royalGold/20 rounded-full flex items-center justify-center">
                <i className={`${info.icon} text-2xl text-royalGold`}></i>
              </div>
              <h3 className="text-xl font-bold text-white mb-3">{info.title}</h3>
              {info.details.map((detail, detailIndex) => (
                <p key={detailIndex} className="text-white/80 leading-relaxed">
                  {detail}
                </p>
              ))}
            </div>
          ))}
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20">
            <h3 className="text-2xl font-serif font-bold text-white mb-6 text-center">Send us a Message</h3>
            
            {submitMessage && (
              <div className="mb-6 p-4 bg-green-500/20 border border-green-500/30 rounded-lg text-green-100">
                {submitMessage}
              </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="fullName" className="block text-white font-medium mb-2">
                    Full Name *
                  </label>
                  <input
                    type="text"
                    id="fullName"
                    name="fullName"
                    value={formData.fullName}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 bg-white/10 border border-white/30 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-royalGold focus:ring-1 focus:ring-royalGold"
                    placeholder="Your full name"
                  />
                </div>
                <div>
                  <label htmlFor="email" className="block text-white font-medium mb-2">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 bg-white/10 border border-white/30 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-royalGold focus:ring-1 focus:ring-royalGold"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="subject" className="block text-white font-medium mb-2">
                  Subject *
                </label>
                <input
                  type="text"
                  id="subject"
                  name="subject"
                  value={formData.subject}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-3 bg-white/10 border border-white/30 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-royalGold focus:ring-1 focus:ring-royalGold"
                  placeholder="What is this regarding?"
                />
              </div>

              <div>
                <label htmlFor="message" className="block text-white font-medium mb-2">
                  Message *
                </label>
                <textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  required
                  rows={6}
                  className="w-full px-4 py-3 bg-white/10 border border-white/30 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-royalGold focus:ring-1 focus:ring-royalGold resize-vertical"
                  placeholder="Your message..."
                ></textarea>
              </div>

              <div className="text-center">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="px-8 py-3 bg-royalGold text-royalBlue font-bold rounded-lg hover:bg-royalGold/90 transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? (
                    <>
                      <i className="fas fa-spinner fa-spin mr-2"></i>
                      Sending...
                    </>
                  ) : (
                    <>
                      <i className="fas fa-paper-plane mr-2"></i>
                      Send Message
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Contact
