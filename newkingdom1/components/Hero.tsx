'use client'

import { motion } from 'framer-motion'

const Hero = () => {
  return (
    <section id="home" className="bg-hero-pattern min-h-screen flex items-center justify-center pt-20 bg-cover bg-center relative overflow-hidden">
      {/* Enhanced gradient overlays */}
      <div className="absolute inset-0 bg-gradient-to-br from-royalBlue/90 via-royalBlue/80 to-royalBlue/95"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/20 via-transparent to-royalGold/10"></div>
      <div className="absolute inset-0 bg-gradient-to-bl from-transparent via-purple-900/20 to-transparent"></div>

      {/* Animated background elements */}
      <div className="absolute inset-0">
        <motion.div
          animate={{
            x: [0, 100, 0],
            y: [0, -50, 0],
            rotate: [0, 180, 360]
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute top-20 right-20 w-64 h-64 bg-gradient-to-br from-royalGold/10 to-transparent rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            x: [0, -80, 0],
            y: [0, 60, 0],
            rotate: [0, -180, -360]
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "linear",
            delay: 5
          }}
          className="absolute bottom-20 left-20 w-48 h-48 bg-gradient-to-tl from-white/10 to-transparent rounded-full blur-2xl"
        />
      </div>

      <div className="container mx-auto px-4 py-16 text-center relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="inline-block mb-6"
        >
          <div className="w-24 h-24 mx-auto relative">
            <motion.svg 
              className="w-full h-full" 
              viewBox="0 0 100 100" 
              fill="none" 
              xmlns="http://www.w3.org/2000/svg"
              animate={{ rotate: 360 }}
              transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
            >
              <path d="M50 5L60 25L82.5 30L65 45L70 67.5L50 55L30 67.5L35 45L17.5 30L40 25L50 5Z" fill="#D4AF37" stroke="#D4AF37" strokeWidth="2"/>
              <circle cx="50" cy="50" r="40" stroke="#D4AF37" strokeWidth="2" strokeDasharray="4 4"/>
              <circle cx="50" cy="50" r="45" stroke="#D4AF37" strokeWidth="1"/>
            </motion.svg>
          </div>
        </motion.div>

        <motion.h1 
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="text-4xl md:text-6xl font-serif font-bold text-white mb-4"
        >
          His Royal Majesty
        </motion.h1>

        <motion.h2 
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="text-2xl md:text-3xl font-serif text-royalGold mb-8"
        >
          King of Ghana
        </motion.h2>

        <motion.p 
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="text-lg text-white/90 max-w-2xl mx-auto mb-10 leading-relaxed"
        >
          Welcome to the official royal website of His Majesty. Discover the heritage, initiatives, and vision that shape our kingdom's future.
        </motion.p>

        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="flex flex-col sm:flex-row justify-center gap-4 mb-16"
        >
          <motion.a
            href="#coronation"
            className="px-8 py-4 relative overflow-hidden text-royalBlue font-bold rounded-lg shadow-2xl border-2 border-yellow-300 group"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            style={{
              background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 25%, #ffd700 50%, #b8860b 75%, #ffd700 100%)',
              boxShadow: '0 12px 40px rgba(255, 215, 0, 0.5), inset 0 3px 6px rgba(255, 255, 255, 0.4), inset 0 -3px 6px rgba(0, 0, 0, 0.3)'
            }}
          >
            {/* Shining animation overlay */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1200 ease-out"></div>
            <span className="relative z-10 font-extrabold">
              <i className="fas fa-crown mr-2"></i>
              Coronation 2025
            </span>
          </motion.a>

          <motion.a
            href="#about-allen"
            className="px-8 py-4 bg-white/10 backdrop-blur-lg border-2 border-royalGold text-white font-bold rounded-lg hover:bg-white/20 transition-all duration-300 shadow-xl"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <i className="fas fa-user-crown mr-2"></i>
            About His Majesty
          </motion.a>
        </motion.div>

        <motion.div 
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 1.2 }}
          className="absolute bottom-10 left-1/2 transform -translate-x-1/2"
        >
          <motion.div
            animate={{ y: [0, -20, 0] }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            className="text-royalGold text-3xl cursor-pointer"
            onClick={() => document.getElementById('highlights')?.scrollIntoView({ behavior: 'smooth' })}
          >
            <i className="fas fa-chevron-down"></i>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}

export default Hero
