'use client'

import React from 'react'
import type { Feature } from '@/types'

const AboutAdukrom = () => {
  const features: Feature[] = [
    {
      icon: '🏛️',
      title: 'Rich Heritage',
      description: 'Centuries of cultural traditions and royal history preserved for future generations.',
      gradient: 'from-blue-500 to-purple-600'
    },
    {
      icon: '🌍',
      title: 'Modern Vision',
      description: 'Embracing innovation and progress while honoring our ancestral wisdom.',
      gradient: 'from-green-500 to-blue-500'
    },
    {
      icon: '🤝',
      title: 'Unity & Peace',
      description: 'Building bridges between communities and fostering harmony across all peoples.',
      gradient: 'from-yellow-500 to-orange-500'
    },
    {
      icon: '📚',
      title: 'Education',
      description: 'Investing in knowledge and learning to empower our youth and communities.',
      gradient: 'from-purple-500 to-pink-500'
    }
  ]

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-serif font-bold text-royalBlue mb-4">About Adukrom Kingdom</h2>
          <div className="w-24 h-1 bg-royalGold mx-auto mb-8"></div>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto leading-relaxed">
            The Adukrom Kingdom stands as a beacon of cultural preservation and progressive leadership. 
            Our commitment to honoring tradition while embracing the future defines our royal mission.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <div
              key={index}
              className="bg-white rounded-xl shadow-lg p-6 text-center hover:shadow-xl transition-shadow duration-300"
            >
              <div className="text-4xl mb-4">{feature.icon}</div>
              <h3 className="text-xl font-bold text-royalBlue mb-3">{feature.title}</h3>
              <p className="text-gray-600 leading-relaxed">{feature.description}</p>
            </div>
          ))}
        </div>

        <div className="mt-16 bg-white rounded-2xl shadow-lg p-8 md:p-12">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-3xl font-serif font-bold text-royalBlue mb-6">Our Royal Mission</h3>
              <p className="text-gray-700 leading-relaxed mb-6">
                The Adukrom Kingdom is dedicated to preserving our rich cultural heritage while building 
                a prosperous and sustainable future for all our people. We believe in the power of unity, 
                education, and progressive leadership.
              </p>
              <p className="text-gray-700 leading-relaxed">
                Through our various initiatives and programs, we work tirelessly to improve the lives 
                of our citizens, promote cultural understanding, and contribute to global peace and development.
              </p>
            </div>
            <div className="text-center">
              <div className="inline-block p-8 bg-gradient-to-br from-royalBlue to-royalBlue/80 rounded-full">
                <div className="text-6xl text-royalGold">👑</div>
              </div>
              <h4 className="text-xl font-bold text-royalBlue mt-4">Royal Seal</h4>
              <p className="text-gray-600 mt-2">Symbol of our commitment to excellence</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default AboutAdukrom
