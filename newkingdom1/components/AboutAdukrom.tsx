'use client'

import { motion } from 'framer-motion'

const AboutAdukrom = () => {
  const features = [
    {
      icon: 'fas fa-mountain',
      title: 'Elevated Location',
      description: 'The Togo Atakora Hills offer natural fortification, cooler climates, and stunning vistas — ideal for eco-tourism and royal ceremonies.',
      gradient: 'from-royalGold to-yellow-500'
    },
    {
      icon: 'fas fa-truck',
      title: 'Trade Connectivity',
      description: 'Sitting on the Ho-Koforidua main trunk road, Adukrom links major economic centers as a burgeoning hub for commerce.',
      gradient: 'from-royalBlue to-blue-600'
    },
    {
      icon: 'fas fa-seedling',
      title: 'Agricultural Potential',
      description: 'Fertile lands and favorable weather conditions make Adukrom a center for sustainable agriculture and agri-business.',
      gradient: 'from-forestGreen to-green-600'
    },
    {
      icon: 'fas fa-bolt',
      title: 'Renewable Energy',
      description: 'The landscape is primed for green energy initiatives, including wind and solar projects aligned with sustainability goals.',
      gradient: 'from-purple-600 to-purple-800'
    }
  ]

  return (
    <section id="about-adukrom" className="py-20 relative overflow-hidden">
      {/* Background with gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-royalBlue via-royalBlue/95 to-royalBlue/90"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/10 via-transparent to-royalGold/5"></div>
      
      {/* Animated background pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="royal-pattern w-full h-full"></div>
      </div>

      {/* Floating elements */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          animate={{ 
            x: [0, -100, 0],
            y: [0, 50, 0],
            rotate: [0, -180, -360]
          }}
          transition={{ 
            duration: 25,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute top-20 left-10 w-96 h-96 bg-gradient-to-br from-royalGold/10 to-transparent rounded-full blur-3xl"
        />
        <motion.div
          animate={{ 
            x: [0, 80, 0],
            y: [0, -30, 0],
            rotate: [0, 90, 180]
          }}
          transition={{ 
            duration: 18,
            repeat: Infinity,
            ease: "linear",
            delay: 5
          }}
          className="absolute bottom-20 right-20 w-64 h-64 bg-gradient-to-tl from-white/10 to-transparent rounded-full blur-2xl"
        />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-serif font-bold text-white mb-4">
            About Adukrom Kingdom
          </h2>
          <div className="w-32 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
          <p className="text-lg text-white/90 max-w-3xl mx-auto leading-relaxed">
            The Crown of Africa - A traditional royal kingdom where heritage meets innovation, 
            empowering our people through cultural preservation, economic development, and international diplomacy.
          </p>
        </motion.div>

        {/* Main content grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
          {/* Left side - Kingdom overview */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            <div className="bg-white/10 backdrop-blur-lg rounded-3xl p-8 border border-white/20 shadow-2xl">
              <div className="flex items-center mb-6">
                <div className="w-16 h-16 bg-gradient-to-br from-royalGold to-yellow-500 rounded-2xl flex items-center justify-center mr-4">
                  <i className="fas fa-crown text-white text-2xl"></i>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-white">Adukrom Kingdom</h3>
                  <p className="text-royalGold">The Crown of Africa</p>
                </div>
              </div>
              
              <p className="text-white/90 leading-relaxed mb-4">
                Nestled atop the serene heights of the Togo Atakora Hills, Adukrom commands breathtaking views and an even more remarkable legacy. As the capital of the Okere District Assembly in Ghana's Eastern Region, Adukrom serves as a gateway between Ghana's vibrant eastern corridor and the broader West African trade routes.
              </p>

              <p className="text-white/90 leading-relaxed mb-6">
                Renowned as the seat of the revered Nifahene Stool of Akuapem, Adukrom is where history breathes through every pathway, and where the future is being shaped by visionary leadership under His Majesty Mpuntuhene Allen Ellison.
              </p>

              <motion.button
                className="text-royalGold hover:text-yellow-300 font-semibold text-sm transition-colors duration-300 flex items-center mb-4"
                whileHover={{ scale: 1.05 }}
              >
                <i className="fas fa-book-open mr-2"></i>
                Read More About Adukrom
              </motion.button>

              <div className="grid grid-cols-2 gap-4">
                <div className="bg-white/10 rounded-xl p-4 text-center">
                  <div className="text-royalGold text-2xl mb-2">
                    <i className="fas fa-crown"></i>
                  </div>
                  <p className="text-white font-semibold">Nifahene</p>
                  <p className="text-white/70 text-sm">Stool of Akuapem</p>
                </div>
                <div className="bg-white/10 rounded-xl p-4 text-center">
                  <div className="text-royalGold text-2xl mb-2">
                    <i className="fas fa-map-marker-alt"></i>
                  </div>
                  <p className="text-white font-semibold">Eastern</p>
                  <p className="text-white/70 text-sm">Region Capital</p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Right side - Vision statement */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <div className="bg-white/10 backdrop-blur-lg rounded-3xl p-8 border border-white/20 shadow-2xl h-full">
              {/* Image placeholder - same size as left box */}
              <div className="w-full h-full min-h-[300px] bg-gradient-to-br from-royalGold/20 to-royalBlue/20 rounded-2xl flex items-center justify-center">
                <div className="text-center">
                  <i className="fas fa-image text-white/50 text-6xl mb-4"></i>
                  <p className="text-white/70 text-lg">Adukrom Kingdom Image</p>
                  <p className="text-white/50 text-sm">Placeholder for kingdom photo</p>
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Features grid */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
        >
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -10, scale: 1.02 }}
              className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300"
            >
              <div className={`w-14 h-14 bg-gradient-to-br ${feature.gradient} rounded-xl flex items-center justify-center mb-4 mx-auto`}>
                <i className={`${feature.icon} text-white text-xl`}></i>
              </div>
              <h4 className="text-lg font-bold text-white text-center mb-3">{feature.title}</h4>
              <p className="text-white/80 text-sm text-center leading-relaxed">{feature.description}</p>
            </motion.div>
          ))}
        </motion.div>

        {/* Call to action */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="bg-white/10 backdrop-blur-lg rounded-3xl p-8 border border-white/20 shadow-2xl max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-white mb-4">Join Our Journey</h3>
            <p className="text-white/90 mb-6 leading-relaxed">
              Be part of a kingdom where tradition empowers transformation, and where every connection 
              contributes to shaping Africa's prosperous future.
            </p>
            <motion.a
              href="#contact"
              className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-royalGold to-yellow-500 text-royalBlue font-bold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <i className="fas fa-handshake mr-2"></i>
              Connect With Us
            </motion.a>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default AboutAdukrom
