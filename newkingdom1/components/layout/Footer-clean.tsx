'use client'

import React from 'react'
import Link from 'next/link'
import type { SocialLink } from '@/types'

interface QuickLink {
  name: string
  href: string
}

const Footer = () => {
  const quickLinks: QuickLink[] = [
    { name: 'Home', href: '/' },
    { name: 'About', href: '#about' },
    { name: 'Events', href: '/events' },
    { name: 'Gallery', href: '/gallery' },
    { name: 'News', href: '/news' },
    { name: 'Contact', href: '#contact' }
  ]

  const socialLinks: SocialLink[] = [
    { icon: 'fab fa-facebook', href: '#', color: 'text-blue-600' },
    { icon: 'fab fa-twitter', href: '#', color: 'text-blue-400' },
    { icon: 'fab fa-instagram', href: '#', color: 'text-pink-600' },
    { icon: 'fab fa-youtube', href: '#', color: 'text-red-600' }
  ]

  return (
    <footer className="bg-royalBlue text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Logo and Description */}
          <div className="md:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="text-3xl">👑</div>
              <span className="text-2xl font-serif font-bold">Adukrom Kingdom</span>
            </div>
            <p className="text-white/80 leading-relaxed mb-6">
              Preserving our rich cultural heritage while building a prosperous future for all. 
              Join us in celebrating the royal traditions and modern vision of the Adukrom Kingdom.
            </p>
            <div className="flex space-x-4">
              {socialLinks.map((social, index) => (
                <Link
                  key={index}
                  href={social.href}
                  className={`w-10 h-10 rounded-full bg-white/10 flex items-center justify-center hover:bg-white/20 transition-colors ${social.color}`}
                >
                  <i className={social.icon}></i>
                </Link>
              ))}
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-bold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              {quickLinks.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-white/80 hover:text-royalGold transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-bold mb-4">Contact</h3>
            <div className="space-y-2 text-white/80">
              <p>
                <i className="fas fa-map-marker-alt mr-2 text-royalGold"></i>
                Adukrom, Ghana
              </p>
              <p>
                <i className="fas fa-phone mr-2 text-royalGold"></i>
                +233 XX XXX XXXX
              </p>
              <p>
                <i className="fas fa-envelope mr-2 text-royalGold"></i>
                <EMAIL>
              </p>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-white/20 mt-8 pt-8 text-center">
          <p className="text-white/60">
            © {new Date().getFullYear()} Adukrom Kingdom. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  )
}

export default Footer
