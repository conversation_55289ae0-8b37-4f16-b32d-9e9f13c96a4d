'use client'

import React from 'react'
import Link from 'next/link'
import type { SocialLink } from '@/types'

interface QuickLink {
  name: string
  href: string
}

const Footer = () => {
  const quickLinks: QuickLink[] = [
    { name: 'Home', href: '/' },
    { name: 'About', href: '#about' },
    { name: 'Royal Family', href: '#family' },
    { name: 'Coronation', href: '#coronation' },
    { name: 'Events', href: '/events' },
    { name: 'Gallery', href: '/gallery' },
    { name: 'News', href: '/news' },
    { name: 'Contact', href: '#contact' }
  ]

  const eventLinks: QuickLink[] = [
    { name: 'Coronation 2025', href: '#coronation' },
    { name: 'RSVP', href: '#rsvp' },
    { name: 'Tickets', href: '/tickets' },
    { name: 'Live Streaming', href: '/streaming' },
    { name: 'Schedule', href: '#schedule' },
    { name: 'Accommodation', href: '#contact' }
  ]

  const resourceLinks: QuickLink[] = [
    { name: 'Royal Initiatives', href: '#initiatives' },
    { name: 'Strategic Partners', href: '/partners' },
    { name: 'Press Releases', href: '/news' },
    { name: 'Media Kit', href: '/media' },
    { name: 'Privacy Policy', href: '/privacy' },
    { name: 'Terms of Service', href: '/terms' }
  ]

  const socialLinks: SocialLink[] = [
    { icon: 'fab fa-facebook', href: 'https://facebook.com/adukromkingdom', color: 'text-blue-600' },
    { icon: 'fab fa-twitter', href: 'https://twitter.com/adukromkingdom', color: 'text-blue-400' },
    { icon: 'fab fa-instagram', href: 'https://instagram.com/adukromkingdom', color: 'text-pink-600' },
    { icon: 'fab fa-youtube', href: 'https://youtube.com/adukromkingdom', color: 'text-red-600' },
    { icon: 'fab fa-linkedin', href: 'https://linkedin.com/company/adukromkingdom', color: 'text-blue-700' },
    { icon: 'fab fa-tiktok', href: 'https://tiktok.com/@adukromkingdom', color: 'text-gray-800' }
  ]

  return (
    <footer className="bg-royalBlue text-white">
      <div className="container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Logo and Description */}
          <div className="lg:col-span-2">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-12 h-12 bg-gradient-to-br from-royalGold to-royalGold/80 rounded-full flex items-center justify-center">
                <span className="text-2xl">👑</span>
              </div>
              <div>
                <span className="text-2xl font-serif font-bold block">Adukrom Kingdom</span>
                <span className="text-sm text-royalGold">Royal Heritage • Modern Vision</span>
              </div>
            </div>
            <p className="text-white/80 leading-relaxed mb-6">
              Preserving our rich cultural heritage while building a prosperous future for all.
              Join us in celebrating the royal traditions and modern vision of the Adukrom Kingdom.
            </p>

            {/* Newsletter Signup */}
            <div className="mb-6">
              <h4 className="text-lg font-bold mb-3">Stay Connected</h4>
              <div className="flex">
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="flex-1 px-4 py-2 bg-white/10 border border-white/30 rounded-l-lg text-white placeholder-white/60 focus:outline-none focus:border-royalGold"
                />
                <button className="px-6 py-2 bg-royalGold text-royalBlue font-bold rounded-r-lg hover:bg-royalGold/90 transition-colors">
                  <i className="fas fa-paper-plane"></i>
                </button>
              </div>
            </div>

            {/* Social Links */}
            <div className="flex space-x-3">
              {socialLinks.map((social, index) => (
                <Link
                  key={index}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 rounded-full bg-white/10 flex items-center justify-center hover:bg-white/20 transition-colors group"
                >
                  <i className={`${social.icon} group-hover:${social.color}`}></i>
                </Link>
              ))}
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-bold mb-4">Navigation</h3>
            <ul className="space-y-2">
              {quickLinks.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-white/80 hover:text-royalGold transition-colors text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Event Links */}
          <div>
            <h3 className="text-lg font-bold mb-4">Coronation 2025</h3>
            <ul className="space-y-2">
              {eventLinks.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-white/80 hover:text-royalGold transition-colors text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Resources & Contact */}
          <div>
            <h3 className="text-lg font-bold mb-4">Resources</h3>
            <ul className="space-y-2 mb-6">
              {resourceLinks.slice(0, 4).map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-white/80 hover:text-royalGold transition-colors text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>

            <h4 className="text-lg font-bold mb-3">Contact</h4>
            <div className="space-y-2 text-white/80 text-sm">
              <p>
                <i className="fas fa-map-marker-alt mr-2 text-royalGold"></i>
                Adukrom, Ghana
              </p>
              <p>
                <i className="fas fa-phone mr-2 text-royalGold"></i>
                +233 XX XXX XXXX
              </p>
              <p>
                <i className="fas fa-envelope mr-2 text-royalGold"></i>
                <EMAIL>
              </p>
            </div>
          </div>
        </div>

        {/* Important Links Bar */}
        <div className="border-t border-white/20 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="flex flex-wrap justify-center md:justify-start gap-6 text-sm">
              <Link href="/privacy" className="text-white/60 hover:text-royalGold transition-colors">
                Privacy Policy
              </Link>
              <Link href="/terms" className="text-white/60 hover:text-royalGold transition-colors">
                Terms of Service
              </Link>
              <Link href="/accessibility" className="text-white/60 hover:text-royalGold transition-colors">
                Accessibility
              </Link>
              <Link href="/sitemap" className="text-white/60 hover:text-royalGold transition-colors">
                Sitemap
              </Link>
            </div>

            <div className="flex items-center space-x-4">
              <span className="text-white/60 text-sm">Available in:</span>
              <button className="text-white/80 hover:text-royalGold transition-colors text-sm">
                🇬🇧 English
              </button>
              <button className="text-white/60 hover:text-royalGold transition-colors text-sm">
                🇬🇭 Twi
              </button>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-white/20 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-white/60 text-sm">
              © {new Date().getFullYear()} Adukrom Kingdom. All rights reserved.
            </p>

            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2 text-white/60 text-sm">
                <i className="fas fa-shield-alt text-royalGold"></i>
                <span>Secured by SSL</span>
              </div>
              <div className="flex items-center space-x-2 text-white/60 text-sm">
                <i className="fas fa-mobile-alt text-royalGold"></i>
                <span>Mobile Optimized</span>
              </div>
            </div>
          </div>

          <div className="text-center mt-4 pt-4 border-t border-white/10">
            <p className="text-white/40 text-xs">
              Built with ❤️ for the people of Ghana and friends of the Adukrom Kingdom worldwide
            </p>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
