'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import type { NavigationItem } from '@/types'

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState<boolean>(false)
  const [isAccountMenuOpen, setIsAccountMenuOpen] = useState<boolean>(false)

  const navigation: NavigationItem[] = [
    { name: 'Home', href: '/' },
    { name: 'About', href: '#about' },
    { name: 'Royal Family', href: '#family' },
    { name: 'Coronation', href: '#coronation' },
    { name: 'Events', href: '/events' },
    { name: 'Gallery', href: '/gallery' },
    { name: 'News', href: '/news' },
    { name: 'Tickets', href: '/tickets' },
    { name: 'Streaming', href: '/streaming' },
    { name: 'Partners', href: '/partners' },
    { name: 'Contact', href: '#contact' }
  ]

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-sm shadow-lg">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-20">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gradient-to-br from-royalBlue to-royalBlue/80 rounded-full flex items-center justify-center">
              <span className="text-2xl">👑</span>
            </div>
            <div>
              <span className="text-xl font-serif font-bold text-royalBlue block">Adukrom Kingdom</span>
              <span className="text-xs text-royalGold font-medium">Royal Heritage • Modern Vision</span>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-6">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href || '#'}
                className="text-gray-700 hover:text-royalBlue transition-colors duration-200 font-medium px-3 py-2 rounded-lg hover:bg-royalBlue/5"
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* Action Buttons */}
          <div className="hidden md:flex items-center space-x-4">
            {/* RSVP Button */}
            <Link
              href="#rsvp"
              className="px-4 py-2 bg-royalGold text-royalBlue font-bold rounded-lg hover:bg-royalGold/90 transition-colors text-sm"
            >
              <i className="fas fa-calendar-check mr-2"></i>
              RSVP
            </Link>

            {/* Tickets Button */}
            <Link
              href="/tickets"
              className="px-4 py-2 bg-royalBlue text-white font-bold rounded-lg hover:bg-royalBlue/90 transition-colors text-sm"
            >
              <i className="fas fa-ticket-alt mr-2"></i>
              Tickets
            </Link>

            {/* Account Menu */}
            <div className="relative">
              <button
                onClick={() => setIsAccountMenuOpen(!isAccountMenuOpen)}
                className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors"
              >
                <i className="fas fa-user text-gray-600"></i>
              </button>

              {/* Account Dropdown */}
              {isAccountMenuOpen && (
                <div className="absolute right-0 top-12 w-48 bg-white rounded-lg shadow-xl border border-gray-200 py-2">
                  <Link
                    href="/login"
                    className="block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors"
                    onClick={() => setIsAccountMenuOpen(false)}
                  >
                    <i className="fas fa-sign-in-alt mr-2"></i>
                    Sign In
                  </Link>
                  <Link
                    href="/register"
                    className="block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors"
                    onClick={() => setIsAccountMenuOpen(false)}
                  >
                    <i className="fas fa-user-plus mr-2"></i>
                    Register
                  </Link>
                  <hr className="my-2" />
                  <Link
                    href="/profile"
                    className="block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors"
                    onClick={() => setIsAccountMenuOpen(false)}
                  >
                    <i className="fas fa-user-circle mr-2"></i>
                    My Profile
                  </Link>
                  <Link
                    href="/my-tickets"
                    className="block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors"
                    onClick={() => setIsAccountMenuOpen(false)}
                  >
                    <i className="fas fa-ticket-alt mr-2"></i>
                    My Tickets
                  </Link>
                </div>
              )}
            </div>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <div className="w-6 h-6 flex flex-col justify-center space-y-1">
              <span className={`block h-0.5 w-6 bg-gray-600 transition-transform ${isMenuOpen ? 'rotate-45 translate-y-1' : ''}`}></span>
              <span className={`block h-0.5 w-6 bg-gray-600 transition-opacity ${isMenuOpen ? 'opacity-0' : ''}`}></span>
              <span className={`block h-0.5 w-6 bg-gray-600 transition-transform ${isMenuOpen ? '-rotate-45 -translate-y-1' : ''}`}></span>
            </div>
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="lg:hidden py-4 border-t border-gray-200">
            <nav className="flex flex-col space-y-2 mb-4">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href || '#'}
                  className="px-4 py-2 text-gray-700 hover:text-royalBlue hover:bg-gray-50 rounded-lg transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}
            </nav>

            {/* Mobile Action Buttons */}
            <div className="px-4 space-y-3">
              <Link
                href="#rsvp"
                className="block w-full px-4 py-3 bg-royalGold text-royalBlue font-bold rounded-lg hover:bg-royalGold/90 transition-colors text-center"
                onClick={() => setIsMenuOpen(false)}
              >
                <i className="fas fa-calendar-check mr-2"></i>
                RSVP for Events
              </Link>
              <Link
                href="/tickets"
                className="block w-full px-4 py-3 bg-royalBlue text-white font-bold rounded-lg hover:bg-royalBlue/90 transition-colors text-center"
                onClick={() => setIsMenuOpen(false)}
              >
                <i className="fas fa-ticket-alt mr-2"></i>
                Get Tickets
              </Link>

              {/* Mobile Account Links */}
              <div className="pt-3 border-t border-gray-200">
                <Link
                  href="/login"
                  className="block px-4 py-2 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <i className="fas fa-sign-in-alt mr-2"></i>
                  Sign In
                </Link>
                <Link
                  href="/register"
                  className="block px-4 py-2 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <i className="fas fa-user-plus mr-2"></i>
                  Register
                </Link>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  )
}

export default Header
