'use client'

import React from 'react'
import { motion } from 'framer-motion'
import type { FamilyMember } from '@/types'

const RoyalFamily = () => {
  const familyMembers: FamilyMember[] = [
    {
      name: 'His Royal Majesty',
      title: 'King of Ghana',
      role: 'Sovereign Ruler',
      description: 'The reigning monarch, dedicated to preserving Ghana\'s heritage while leading the nation into a prosperous future.',
      gradient: 'from-royalGold to-yellow-500',
      icon: 'fas fa-crown'
    },
    {
      name: 'Her Royal Majesty',
      title: 'Queen Consort',
      role: 'Royal Patron',
      description: 'Champion of women\'s empowerment and education, leading numerous charitable initiatives across the kingdom.',
      gradient: 'from-purple-500 to-pink-500',
      icon: 'fas fa-gem'
    },
    {
      name: 'Prince <PERSON>',
      title: 'Crown Prince',
      role: 'Heir Apparent',
      description: 'Next in line to the throne, focusing on youth development and technological advancement in Ghana.',
      gradient: 'from-royalBlue to-blue-600',
      icon: 'fas fa-chess-king'
    },
    {
      name: 'Princess <PERSON><PERSON><PERSON>',
      title: 'Royal Princess',
      role: 'Cultural Ambassador',
      description: 'Dedicated to preserving Ghanaian arts and culture, promoting traditional crafts and festivals worldwide.',
      gradient: 'from-forestGreen to-green-600',
      icon: 'fas fa-palette'
    }
  ]



  return (
    <section id="royal-family" className="py-20 relative overflow-hidden">
      {/* Background with gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-cream via-ivory to-cream"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/5 via-transparent to-royalBlue/5"></div>
      
      {/* Floating background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          animate={{ 
            x: [0, 150, 0],
            y: [0, -100, 0],
            rotate: [0, 360, 720]
          }}
          transition={{ 
            duration: 30,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute top-20 right-20 w-80 h-80 bg-gradient-to-br from-royalGold/8 to-transparent rounded-full blur-3xl"
        />
        <motion.div
          animate={{ 
            x: [0, -120, 0],
            y: [0, 80, 0],
            rotate: [0, -180, -360]
          }}
          transition={{ 
            duration: 25,
            repeat: Infinity,
            ease: "linear",
            delay: 10
          }}
          className="absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-tl from-royalBlue/8 to-transparent rounded-full blur-3xl"
        />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-serif font-bold bg-gradient-to-r from-royalBlue to-royalBlue/80 bg-clip-text text-transparent mb-4">
            The Royal Family
          </h2>
          <div className="w-32 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto leading-relaxed">
            Meet the distinguished members of the Royal Family, each dedicated to serving Ghana and its people with honor, wisdom, and compassion.
          </p>
        </motion.div>

        {/* Royal Family Members */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
          {familyMembers.map((member, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -10, scale: 1.02 }}
              className="group relative"
            >
              <div className="bg-white/60 backdrop-blur-lg rounded-3xl p-6 border border-white/50 shadow-xl hover:shadow-2xl transition-all duration-300">
                {/* Profile Image Placeholder */}
                <div className={`w-24 h-24 mx-auto mb-4 bg-gradient-to-br ${member.gradient} rounded-full flex items-center justify-center shadow-lg`}>
                  <i className={`${member.icon} text-white text-2xl`}></i>
                </div>

                {/* Member Info */}
                <div className="text-center">
                  <h3 className="text-xl font-bold text-royalBlue mb-1">{member.name}</h3>
                  <p className="text-royalGold font-semibold mb-2">{member.title}</p>
                  <div className="bg-gradient-to-r from-gray-100 to-gray-50 rounded-lg p-3 mb-4">
                    <p className="text-sm font-medium text-gray-600">{member.role}</p>
                  </div>
                  <p className="text-gray-700 text-sm leading-relaxed">{member.description}</p>
                </div>

                {/* Hover effect overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-royalGold/10 to-transparent rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
            </motion.div>
          ))}
        </div>



        {/* Family Values */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <div className="bg-gradient-to-r from-royalBlue/10 to-royalGold/10 backdrop-blur-lg rounded-3xl p-8 border border-white/30 shadow-2xl max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-royalBlue mb-6">Royal Family Values</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 mx-auto mb-3 bg-gradient-to-br from-royalGold to-yellow-500 rounded-xl flex items-center justify-center">
                  <i className="fas fa-heart text-white"></i>
                </div>
                <h4 className="font-semibold text-royalBlue mb-2">Service</h4>
                <p className="text-gray-700 text-sm">Dedicated to serving the people of Ghana with humility and compassion</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 mx-auto mb-3 bg-gradient-to-br from-royalBlue to-blue-600 rounded-xl flex items-center justify-center">
                  <i className="fas fa-balance-scale text-white"></i>
                </div>
                <h4 className="font-semibold text-royalBlue mb-2">Integrity</h4>
                <p className="text-gray-700 text-sm">Upholding the highest standards of honesty and moral principles</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 mx-auto mb-3 bg-gradient-to-br from-forestGreen to-green-600 rounded-xl flex items-center justify-center">
                  <i className="fas fa-seedling text-white"></i>
                </div>
                <h4 className="font-semibold text-royalBlue mb-2">Legacy</h4>
                <p className="text-gray-700 text-sm">Building a lasting legacy for future generations of Ghanaians</p>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default RoyalFamily
