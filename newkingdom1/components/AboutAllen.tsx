'use client'

import React from 'react'
import { motion } from 'framer-motion'

const AboutAllen = (): JSX.Element => {
  return (
    <section id="about-allen" className="py-20 relative overflow-hidden">
      {/* Background with gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-cream via-ivory to-cream"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/5 via-transparent to-royalBlue/5"></div>
      
      {/* Floating background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          animate={{ 
            x: [0, 100, 0],
            y: [0, -50, 0],
            rotate: [0, 180, 360]
          }}
          transition={{ 
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute top-10 right-10 w-64 h-64 bg-gradient-to-br from-royalGold/10 to-transparent rounded-full blur-3xl"
        />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-serif font-bold bg-gradient-to-r from-royalBlue to-royalBlue/80 bg-clip-text text-transparent mb-4">
            About His Royal Majesty
          </h2>
          <div className="w-32 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto leading-relaxed">
            Discover the remarkable journey of His Royal Majesty, a visionary leader dedicated to preserving Ghana's rich heritage while embracing modern innovation.
          </p>
        </motion.div>

        {/* Biography Section with Image */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-start mb-12">
          {/* Biography Card */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="bg-white/60 backdrop-blur-lg rounded-2xl p-6 border border-white/50 shadow-xl">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-royalGold to-yellow-500 rounded-xl flex items-center justify-center mr-4">
                  <i className="fas fa-user text-white text-xl"></i>
                </div>
                <h4 className="text-xl font-bold text-royalBlue">Biography</h4>
              </div>
              <p className="text-gray-700 leading-relaxed mb-4">
                His Majesty King Allen Ellison, globally known for his visionary leadership, is being enstooled Mpuntuhene of Adukrom, Eastern Region of Ghana—an esteemed royal office dedicated to advancing trade, investment, innovation and economic development for the Kingdom and beyond.
              </p>
              <p className="text-gray-700 leading-relaxed mb-4">
                Born in Avon Park, Florida, USA, Allen Ellison has risen from humble beginnings to become one of the most dynamic royal figures of the 21st century. With a Bachelor of Arts degree in Political Science and Business Administration from Florida Southern College, he has spent over two decades driving economic empowerment, financial literacy, and sustainable development across communities in the United States, Asia the Caribbean, and Africa.
              </p>

              <motion.button
                className="text-royalBlue hover:text-royalGold font-semibold text-sm transition-colors duration-300 flex items-center"
                whileHover={{ scale: 1.05 }}
              >
                <i className="fas fa-book-open mr-2"></i>
                Read More About Allen
              </motion.button>
            </div>
          </motion.div>

          {/* Image/Profile Section */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="relative"
          >
            <div className="relative">
              {/* Main profile container */}
              <div className="bg-white/40 backdrop-blur-lg rounded-3xl p-8 border border-white/50 shadow-2xl">
                <div className="bg-gradient-to-br from-royalBlue/90 to-royalBlue rounded-2xl p-8 text-center">
                  <div className="w-48 h-48 mx-auto mb-6 bg-gradient-to-br from-royalGold/30 to-royalGold/10 rounded-full flex items-center justify-center border-4 border-royalGold/30">
                    <svg className="w-32 h-32" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <circle cx="50" cy="35" r="18" fill="#D4AF37"/>
                      <path d="M50 60C35 60 23 72 23 87H77C77 72 65 60 50 60Z" fill="#D4AF37"/>
                      <circle cx="50" cy="50" r="40" stroke="#D4AF37" strokeWidth="2" strokeDasharray="3 3"/>
                    </svg>
                  </div>
                  <h3 className="text-2xl font-serif font-bold text-white mb-2">His Royal Majesty</h3>
                  <p className="text-royalGold text-lg mb-4">King Allen Ellison</p>
                  <div className="text-sm text-white/80 space-y-2">
                    <p><span className="text-royalGold">Born:</span> Avon Park, Florida, USA</p>
                    <p><span className="text-royalGold">Education:</span> Florida Southern College</p>
                    <p><span className="text-royalGold">Enstoolment:</span> August 29, 2025</p>
                  </div>
                </div>
              </div>

              {/* Decorative elements */}
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-br from-royalGold/30 to-transparent rounded-full blur-xl"></div>
              <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-gradient-to-tr from-royalBlue/20 to-transparent rounded-full blur-xl"></div>
            </div>
          </motion.div>
        </div>

        {/* Vision, Mission, and Royal Message Section */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Content Section */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="space-y-8 lg:col-span-3"
          >

            {/* Vision Card */}
            <div className="bg-white/60 backdrop-blur-lg rounded-2xl p-6 border border-white/50 shadow-xl">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-royalBlue to-blue-600 rounded-xl flex items-center justify-center mr-4">
                  <i className="fas fa-eye text-white text-xl"></i>
                </div>
                <h4 className="text-xl font-bold text-royalBlue">Vision</h4>
              </div>
              <p className="text-gray-700 leading-relaxed">
                Adukrom will shine as The Crown of Africa — a beacon of cultural pride, sovereign strength, and innovative leadership, setting the standard for how kingdoms can drive 21st-century progress across the continent and beyond.
              </p>
            </div>

            {/* Mission Card */}
            <div className="bg-white/60 backdrop-blur-lg rounded-2xl p-6 border border-white/50 shadow-xl">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-forestGreen to-green-600 rounded-xl flex items-center justify-center mr-4">
                  <i className="fas fa-target text-white text-xl"></i>
                </div>
                <h4 className="text-xl font-bold text-royalBlue">Mission</h4>
              </div>
              <p className="text-gray-700 leading-relaxed">
                To lead the Rebirth of Adukrom by uniting royal heritage with transformative economic development, fostering prosperity, sustainability, and global partnerships that empower our people and inspire Africa's rise.
              </p>
            </div>

            {/* Call to Action */}
            <motion.div
              whileHover={{ scale: 1.02 }}
              className="bg-gradient-to-r from-royalGold/20 to-yellow-400/20 backdrop-blur-lg rounded-2xl p-6 border border-royalGold/30 shadow-xl"
            >
              <div className="text-center">
                <h4 className="text-xl font-bold text-royalBlue mb-3">Royal Message</h4>
                <p className="text-gray-700 italic mb-4">
                  "Our kingdom's strength lies in honoring our ancestors while building bridges to the future. Together, we will create prosperity that respects our traditions and embraces innovation for The Rebirth of Adukrom."
                </p>
                <p className="text-right text-royalBlue font-semibold">— King Allen Ellison</p>
                <motion.a
                  href="#royal-message"
                  className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-royalBlue to-blue-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <i className="fas fa-quote-right mr-2"></i>
                  Read Full Message
                </motion.a>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default AboutAllen
