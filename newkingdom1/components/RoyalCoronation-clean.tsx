'use client'

import React, { useState, useEffect } from 'react'
import type { TimeLeft } from '@/types'

interface ScheduleItem {
  time: string
  event: string
  location: string
}

interface EventDay {
  day: string
  icon: string
  schedule: ScheduleItem[]
}

const RoyalCoronation = () => {
  const [timeLeft, setTimeLeft] = useState<TimeLeft>({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  })

  useEffect(() => {
    const targetDate = new Date('2025-04-15T10:00:00').getTime()

    const timer = setInterval(() => {
      const now = new Date().getTime()
      const difference = targetDate - now

      const days = Math.floor(difference / (1000 * 60 * 60 * 24))
      const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
      const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60))
      const seconds = Math.floor((difference % (1000 * 60)) / 1000)

      setTimeLeft({ days, hours, minutes, seconds })

      if (difference < 0) {
        clearInterval(timer)
        setTimeLeft({ days: 0, hours: 0, minutes: 0, seconds: 0 })
      }
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  const events: EventDay[] = [
    {
      day: 'Day 1: April 14, 2025',
      icon: 'fas fa-sun',
      schedule: [
        { time: '9:00 AM', event: 'Traditional Purification Ceremony', location: 'Manhyia Palace' },
        { time: '2:00 PM', event: 'Royal Procession', location: 'Accra City Center' },
        { time: '7:00 PM', event: 'Cultural Performances', location: 'National Theatre' }
      ]
    },
    {
      day: 'Day 2: April 15, 2025',
      icon: 'fas fa-crown',
      schedule: [
        { time: '10:00 AM', event: 'Coronation Ceremony', location: 'Accra International Conference Centre' },
        { time: '2:00 PM', event: 'Royal Address', location: 'Independence Square' },
        { time: '7:00 PM', event: 'Royal Banquet', location: 'State House' }
      ]
    }
  ]

  return (
    <section id="coronation" className="py-20 bg-royalBlue relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="royal-pattern w-full h-full"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-serif font-bold text-white mb-4">Coronation 2025</h2>
          <div className="w-24 h-1 bg-royalGold mx-auto mb-4"></div>
          <p className="max-w-3xl mx-auto text-white/80 leading-relaxed">
            Join us for the historic coronation ceremony of His Royal Majesty, a once-in-a-generation celebration of Ghana's royal heritage.
          </p>
        </div>

        {/* Main Event Card */}
        <div className="bg-white rounded-2xl shadow-2xl overflow-hidden mb-16">
          <div className="flex flex-col md:flex-row">
            {/* Countdown Section */}
            <div className="md:w-1/2 bg-gradient-to-br from-royalGold/20 to-royalGold/10 p-8 flex items-center justify-center">
              <div className="text-center">
                <div className="text-royalGold text-6xl mb-6">
                  <i className="fas fa-crown"></i>
                </div>
                <h3 className="text-2xl font-serif font-bold text-royalBlue mb-2">Royal Coronation</h3>
                <p className="text-royalBlue/70 mb-8">A Historic Celebration</p>
                
                {/* Countdown Timer */}
                <div className="flex justify-center space-x-4">
                  {Object.entries(timeLeft).map(([unit, value]) => (
                    <div
                      key={unit}
                      className="bg-white rounded-lg shadow-lg p-4 text-center min-w-[80px]"
                    >
                      <div className="text-royalGold font-bold text-2xl">
                        {value}
                      </div>
                      <div className="text-xs text-gray-600 capitalize">{unit}</div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Event Details */}
            <div className="md:w-1/2 p-8">
              <h3 className="text-xl font-bold text-royalBlue mb-4">Event Overview</h3>
              <p className="text-gray-700 mb-4 leading-relaxed">
                The coronation of His Royal Majesty will be a magnificent celebration of Ghana's cultural heritage and a momentous occasion in the nation's history.
              </p>
              <p className="text-gray-700 mb-6 leading-relaxed">
                Distinguished guests from across Africa and around the globe will join Ghanaian citizens in witnessing this historic ceremony.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4">
                <a
                  href="#schedule"
                  className="px-6 py-3 bg-royalGold text-royalBlue font-bold rounded-lg hover:bg-royalGold/90 transition-colors text-center"
                >
                  <i className="fas fa-calendar-alt mr-2"></i>
                  View Schedule
                </a>
                <a 
                  href="#tickets" 
                  className="px-6 py-3 bg-royalBlue text-white font-bold rounded-lg hover:bg-royalBlue/90 transition-colors text-center"
                >
                  <i className="fas fa-ticket-alt mr-2"></i>
                  Get Tickets
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Event Schedule */}
        <div id="schedule" className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {events.map((eventDay, dayIndex) => (
            <div
              key={dayIndex}
              className="bg-white rounded-xl shadow-lg overflow-hidden"
            >
              <div className="bg-gradient-to-r from-royalBlue to-royalBlue/80 text-white p-6">
                <div className="flex items-center">
                  <div className="w-12 h-12 rounded-full bg-royalGold/20 flex items-center justify-center text-royalGold mr-4">
                    <i className={eventDay.icon}></i>
                  </div>
                  <h3 className="font-serif font-bold text-lg">{eventDay.day}</h3>
                </div>
              </div>
              
              <div className="p-6">
                <div className="space-y-6">
                  {eventDay.schedule.map((item, index) => (
                    <div
                      key={index}
                      className="relative pl-8 border-l-2 border-royalGold/30"
                    >
                      <div className="absolute -left-2 w-4 h-4 rounded-full bg-royalGold"></div>
                      <div>
                        <p className="text-sm font-bold text-gray-800">{item.time} - {item.event}</p>
                        <p className="text-xs text-gray-600 mt-1">
                          <i className="fas fa-map-marker-alt text-royalGold mr-1"></i>
                          {item.location}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16">
          <p className="text-white/80 mb-6">
            For accommodation and travel information, please contact our Royal Hospitality Team.
          </p>
          <a
            href="#contact"
            className="inline-flex items-center px-8 py-4 bg-royalGold text-royalBlue font-bold rounded-lg hover:bg-royalGold/90 transition-colors"
          >
            <i className="fas fa-phone mr-2"></i>
            Contact Hospitality Team
          </a>
        </div>
      </div>
    </section>
  )
}

export default RoyalCoronation
