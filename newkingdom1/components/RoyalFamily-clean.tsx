'use client'

import React from 'react'
import type { FamilyMember } from '@/types'

const RoyalFamily = () => {
  const familyMembers: FamilyMember[] = [
    {
      name: 'His Royal Majesty',
      title: 'King of Adukrom',
      role: 'Sovereign Ruler',
      description: 'Leading the kingdom with wisdom, compassion, and a vision for the future.',
      gradient: 'from-blue-600 to-purple-700',
      icon: '👑'
    },
    {
      name: 'Her Royal Majesty',
      title: 'Queen of Adukrom',
      role: 'Royal Consort',
      description: 'Championing education, healthcare, and cultural preservation initiatives.',
      gradient: 'from-purple-600 to-pink-600',
      icon: '👸'
    },
    {
      name: 'Royal Prince',
      title: 'Crown Prince',
      role: 'Heir Apparent',
      description: 'Preparing for future leadership while supporting youth development programs.',
      gradient: 'from-green-600 to-blue-600',
      icon: '🤴'
    },
    {
      name: 'Royal Princess',
      title: 'Princess of Adukrom',
      role: 'Cultural Ambassador',
      description: 'Promoting arts, culture, and international relations for the kingdom.',
      gradient: 'from-pink-600 to-purple-600',
      icon: '👸'
    }
  ]

  return (
    <section id="family" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-serif font-bold text-royalBlue mb-4">The Royal Family</h2>
          <div className="w-24 h-1 bg-royalGold mx-auto mb-8"></div>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto leading-relaxed">
            Meet the distinguished members of the Adukrom Royal Family, each dedicated to serving 
            our people and preserving our noble traditions while building a brighter future.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {familyMembers.map((member, index) => (
            <div
              key={index}
              className="bg-gradient-to-br from-gray-50 to-white rounded-2xl shadow-lg p-6 text-center hover:shadow-xl transition-all duration-300 hover:-translate-y-2"
            >
              <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-royalBlue to-royalBlue/80 rounded-full flex items-center justify-center">
                <span className="text-3xl">{member.icon}</span>
              </div>
              
              <h3 className="text-xl font-bold text-royalBlue mb-2">{member.name}</h3>
              <p className="text-royalGold font-semibold mb-1">{member.title}</p>
              <p className="text-gray-600 text-sm mb-4">{member.role}</p>
              <p className="text-gray-700 text-sm leading-relaxed">{member.description}</p>
            </div>
          ))}
        </div>

        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-royalBlue to-royalBlue/90 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-serif font-bold mb-4">Royal Lineage</h3>
            <p className="text-white/90 leading-relaxed max-w-3xl mx-auto">
              The Adukrom Royal Family has served our people for generations, maintaining a legacy 
              of leadership, service, and dedication to the welfare of all citizens. Our commitment 
              to excellence and progress continues to guide our kingdom toward a prosperous future.
            </p>
          </div>
        </div>
      </div>
    </section>
  )
}

export default RoyalFamily
