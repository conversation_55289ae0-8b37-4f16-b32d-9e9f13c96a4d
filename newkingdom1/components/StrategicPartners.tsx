'use client'

import React from 'react'
import type { Partner } from '@/types'

const StrategicPartners = () => {
  const partners: Partner[] = [
    {
      name: 'United Nations',
      logo: '/images/partners/un-logo.png',
      description: 'Collaborating on sustainable development goals and humanitarian initiatives.',
      category: 'International Organizations',
      website: 'https://www.un.org'
    },
    {
      name: 'African Union',
      logo: '/images/partners/au-logo.png',
      description: 'Promoting African unity and continental development programs.',
      category: 'Regional Organizations',
      website: 'https://au.int'
    },
    {
      name: 'World Bank',
      logo: '/images/partners/worldbank-logo.png',
      description: 'Supporting economic development and poverty reduction initiatives.',
      category: 'Financial Institutions',
      website: 'https://www.worldbank.org'
    },
    {
      name: 'UNESCO',
      logo: '/images/partners/unesco-logo.png',
      description: 'Preserving cultural heritage and promoting education for all.',
      category: 'Cultural Organizations',
      website: 'https://www.unesco.org'
    },
    {
      name: 'Ghana Government',
      logo: '/images/partners/ghana-gov-logo.png',
      description: 'Working together for national development and citizen welfare.',
      category: 'Government Partners',
      website: 'https://www.ghana.gov.gh'
    },
    {
      name: 'ECOWAS',
      logo: '/images/partners/ecowas-logo.png',
      description: 'Strengthening West African economic integration and cooperation.',
      category: 'Regional Organizations',
      website: 'https://www.ecowas.int'
    }
  ]

  const partnerCategories = [
    'All Partners',
    'International Organizations',
    'Regional Organizations',
    'Financial Institutions',
    'Cultural Organizations',
    'Government Partners'
  ]

  const [selectedCategory, setSelectedCategory] = React.useState('All Partners')

  const filteredPartners = selectedCategory === 'All Partners' 
    ? partners 
    : partners.filter(partner => partner.category === selectedCategory)

  return (
    <section id="partners" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-serif font-bold text-royalBlue mb-4">Strategic Partners</h2>
          <div className="w-24 h-1 bg-royalGold mx-auto mb-8"></div>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto leading-relaxed">
            We collaborate with distinguished organizations worldwide to advance our shared goals 
            of peace, development, and cultural preservation.
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {partnerCategories.map((category) => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`px-6 py-2 rounded-full font-medium transition-all duration-300 ${
                selectedCategory === category
                  ? 'bg-royalBlue text-white shadow-lg'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {category}
            </button>
          ))}
        </div>

        {/* Partners Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredPartners.map((partner, index) => (
            <div
              key={index}
              className="bg-white border border-gray-200 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2 overflow-hidden"
            >
              {/* Partner Logo */}
              <div className="h-32 bg-gray-50 flex items-center justify-center p-6">
                <div className="w-full h-full bg-gray-200 rounded-lg flex items-center justify-center">
                  <span className="text-gray-500 text-sm">Logo</span>
                </div>
              </div>

              {/* Partner Info */}
              <div className="p-6">
                <div className="mb-3">
                  <span className="inline-block px-3 py-1 bg-royalBlue/10 text-royalBlue text-xs font-semibold rounded-full">
                    {partner.category}
                  </span>
                </div>
                
                <h3 className="text-xl font-bold text-royalBlue mb-3">{partner.name}</h3>
                <p className="text-gray-700 leading-relaxed mb-4">{partner.description}</p>
                
                <div className="flex items-center justify-between">
                  <a
                    href={partner.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-royalBlue font-semibold hover:text-royalBlue/80 transition-colors"
                  >
                    Visit Website
                    <i className="fas fa-external-link-alt ml-2 text-xs"></i>
                  </a>
                  <button className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center hover:bg-royalBlue hover:text-white transition-colors">
                    <i className="fas fa-share-alt text-xs"></i>
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Partnership Benefits */}
        <div className="mt-16 bg-gradient-to-r from-royalBlue/5 to-royalGold/5 rounded-2xl p-8 md:p-12">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-serif font-bold text-royalBlue mb-4">Partnership Benefits</h3>
            <p className="text-gray-700 leading-relaxed max-w-2xl mx-auto">
              Our strategic partnerships enable us to leverage collective expertise and resources 
              for maximum impact across all our initiatives.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-royalBlue/10 rounded-full flex items-center justify-center">
                <i className="fas fa-handshake text-2xl text-royalBlue"></i>
              </div>
              <h4 className="text-lg font-bold text-royalBlue mb-2">Collaborative Impact</h4>
              <p className="text-gray-700 text-sm leading-relaxed">
                Working together to achieve greater results than any single organization could accomplish alone.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-royalBlue/10 rounded-full flex items-center justify-center">
                <i className="fas fa-globe text-2xl text-royalBlue"></i>
              </div>
              <h4 className="text-lg font-bold text-royalBlue mb-2">Global Reach</h4>
              <p className="text-gray-700 text-sm leading-relaxed">
                Extending our influence and impact through established international networks and expertise.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-royalBlue/10 rounded-full flex items-center justify-center">
                <i className="fas fa-lightbulb text-2xl text-royalBlue"></i>
              </div>
              <h4 className="text-lg font-bold text-royalBlue mb-2">Innovation</h4>
              <p className="text-gray-700 text-sm leading-relaxed">
                Sharing knowledge and best practices to develop innovative solutions for complex challenges.
              </p>
            </div>
          </div>
        </div>

        {/* Partnership Opportunities */}
        <div className="mt-16 text-center">
          <div className="bg-white border-2 border-royalBlue/20 rounded-2xl p-8">
            <h3 className="text-2xl font-serif font-bold text-royalBlue mb-4">Become a Partner</h3>
            <p className="text-gray-700 mb-6 max-w-2xl mx-auto leading-relaxed">
              We welcome organizations that share our vision and values to join us in creating 
              positive change. Together, we can build a better future for all.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="px-6 py-3 bg-royalBlue text-white font-bold rounded-lg hover:bg-royalBlue/90 transition-colors">
                <i className="fas fa-handshake mr-2"></i>
                Partnership Inquiry
              </button>
              <button className="px-6 py-3 bg-royalGold text-royalBlue font-bold rounded-lg hover:bg-royalGold/90 transition-colors">
                <i className="fas fa-download mr-2"></i>
                Partnership Guidelines
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default StrategicPartners
