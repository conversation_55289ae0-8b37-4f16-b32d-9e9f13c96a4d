'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'

interface TabContent {
  title: string
  content: React.ReactNode
}

interface TabContentMap {
  [key: string]: TabContent
}

const About = () => {
  const [activeTab, setActiveTab] = useState<string>('biography')

  const tabs = [
    { id: 'biography', name: 'Biography', icon: 'fas fa-book' },
    { id: 'vision', name: 'Vision & Mission', icon: 'fas fa-eye' },
    { id: 'lineage', name: 'Royal Heritage', icon: 'fas fa-sitemap' },
    { id: 'message', name: 'Royal Message', icon: 'fas fa-quote-left' },
  ]

  const tabContent: TabContentMap = {
    biography: {
      title: 'Biography',
      content: (
        <div className="space-y-6">
          <p className="text-gray-700 leading-relaxed">
            His <PERSON> was born into the noble lineage of the Ashanti Dynasty, one of Ghana's most revered royal houses. From an early age, he was groomed in the traditions and customs of royal leadership, receiving both traditional education and modern academic training.
          </p>
          <p className="text-gray-700 leading-relaxed">
            After completing his education at prestigious institutions both in Ghana and abroad, His <PERSON> dedicated himself to public service and cultural preservation. His work in sustainable development and cultural heritage has earned him numerous accolades and the respect of leaders worldwide.
          </p>
          <p className="text-gray-700 leading-relaxed">
            Prior to his ascension to the throne, His Majesty served as a cultural ambassador, representing Ghana at international forums and building bridges between tradition and modernity.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
            {[
              { icon: 'fas fa-graduation-cap', title: 'Education', details: 'Oxford University\nUniversity of Ghana' },
              { icon: 'fas fa-language', title: 'Languages', details: 'Twi, English, French\nHausa, Ewe' },
              { icon: 'fas fa-award', title: 'Honors', details: 'Order of the Volta\nUNESCO Heritage Award' }
            ].map((item, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="bg-royalBlue/5 p-6 rounded-lg text-center hover:bg-royalBlue/10 transition-colors duration-300"
              >
                <div className="text-royalGold text-3xl mb-3">
                  <i className={item.icon}></i>
                </div>
                <h5 className="font-bold text-royalBlue mb-2">{item.title}</h5>
                <p className="text-sm text-gray-600 whitespace-pre-line">{item.details}</p>
              </motion.div>
            ))}
          </div>
        </div>
      )
    },
    vision: {
      title: 'Vision & Mission',
      content: (
        <div className="space-y-8">
          <div>
            <h4 className="text-xl font-bold text-royalBlue mb-4">Royal Vision</h4>
            <p className="text-gray-700 leading-relaxed">
              To establish Ghana as a beacon of cultural preservation and sustainable development in Africa, where traditional wisdom and modern innovation harmoniously coexist to create prosperity for all citizens.
            </p>
          </div>
          
          <div>
            <h4 className="text-xl font-bold text-royalBlue mb-4">Royal Mission</h4>
            <p className="text-gray-700 leading-relaxed">
              To lead with wisdom and compassion, honoring our ancestral heritage while embracing positive change, fostering unity among diverse communities, and ensuring that Ghana's cultural and natural resources benefit present and future generations.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
            {[
              { icon: 'fas fa-landmark', title: 'Cultural Preservation', description: 'Safeguarding Ghana\'s rich cultural heritage through documentation, education, and celebration of traditional arts, crafts, and knowledge systems.' },
              { icon: 'fas fa-seedling', title: 'Sustainable Development', description: 'Promoting economic growth that respects environmental boundaries and ensures equitable distribution of resources across all communities.' },
              { icon: 'fas fa-hands-helping', title: 'Community Empowerment', description: 'Supporting local initiatives that strengthen community bonds, preserve traditional governance systems, and empower youth and women.' },
              { icon: 'fas fa-globe-africa', title: 'Global Partnerships', description: 'Building strategic alliances with international organizations and other nations to advance Ghana\'s interests and contribute to global peace and prosperity.' }
            ].map((item, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="bg-royalBlue/5 p-6 rounded-lg hover:bg-royalBlue/10 transition-colors duration-300"
              >
                <div className="flex items-center mb-3">
                  <div className="w-12 h-12 rounded-full bg-royalGold/20 flex items-center justify-center text-royalGold mr-4">
                    <i className={item.icon}></i>
                  </div>
                  <h5 className="font-bold text-royalBlue">{item.title}</h5>
                </div>
                <p className="text-gray-600 text-sm leading-relaxed">{item.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      )
    },
    lineage: {
      title: 'Royal Heritage',
      content: (
        <div className="space-y-6">
          <p className="text-gray-700 leading-relaxed">
            The royal lineage of Ghana traces back through centuries of distinguished leadership and cultural stewardship. His Majesty's ancestry connects directly to the founders of the great Ashanti Kingdom, whose wisdom and courage shaped the nation's history.
          </p>
          
          <div className="relative py-8">
            <div className="absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-royalGold/30"></div>
            
            {[
              { title: 'His Royal Majesty', subtitle: 'Current King of Ghana (2025-Present)', current: true },
              { title: 'King Osei Tutu II', subtitle: 'Predecessor (1999-2024)', description: 'Led Ghana through a period of cultural renaissance and economic growth, establishing key international partnerships.' },
              { title: 'King Opoku Ware II', subtitle: 'Great Predecessor (1970-1999)', description: 'Guided the kingdom through post-independence challenges, preserving traditional governance while adapting to modern realities.' },
              { title: 'Ancient Royal Lineage', subtitle: 'Dating back to the 17th century', description: 'A continuous line of distinguished leaders who established the Ashanti Kingdom as a powerful force in West African history and culture.' }
            ].map((ruler, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                className={`relative z-10 mb-12 ${index % 2 === 0 ? 'ml-16' : 'mr-16 ml-auto'}`}
              >
                <div className="flex items-center mb-3">
                  <div className={`w-12 h-12 rounded-full ${ruler.current ? 'bg-royalGold' : 'bg-royalGold/70'} flex items-center justify-center text-white absolute left-1/2 transform -translate-x-1/2 ${index % 2 === 0 ? '-ml-16' : '-mr-16'}`}>
                    <i className="fas fa-crown"></i>
                  </div>
                </div>
                <div className={`${ruler.current ? 'bg-royalBlue text-white' : 'bg-white border-l-4 border-royalGold'} p-6 rounded-lg shadow-md`}>
                  <h4 className={`font-bold ${ruler.current ? 'text-white' : 'text-royalBlue'}`}>{ruler.title}</h4>
                  <p className={`text-sm ${ruler.current ? 'text-royalGold' : 'text-gray-600'} mb-2`}>{ruler.subtitle}</p>
                  {ruler.description && (
                    <p className={`text-sm ${ruler.current ? 'text-white/90' : 'text-gray-700'} leading-relaxed`}>{ruler.description}</p>
                  )}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      )
    },
    message: {
      title: 'Message from the King',
      content: (
        <div className="flex flex-col md:flex-row gap-8">
          <div className="md:w-1/3 flex justify-center">
            <motion.div 
              initial={{ scale: 0.8, opacity: 0 }}
              whileInView={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.6 }}
              className="w-48 h-48 rounded-full bg-royalBlue/10 flex items-center justify-center"
            >
              <div className="w-40 h-40 rounded-full bg-royalGold/20 flex items-center justify-center">
                <svg className="w-24 h-24" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="50" cy="40" r="20" fill="#D4AF37"/>
                  <path d="M50 65C33.4315 65 20 78.4315 20 95H80C80 78.4315 66.5685 65 50 65Z" fill="#D4AF37"/>
                  <circle cx="50" cy="50" r="45" stroke="#D4AF37" strokeWidth="2"/>
                </svg>
              </div>
            </motion.div>
          </div>
          <div className="md:w-2/3">
            <motion.blockquote 
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="italic text-gray-700 space-y-4 leading-relaxed"
            >
              <p>"My beloved people of Ghana and friends around the world,</p>
              <p>As I prepare to assume the sacred responsibility of leadership, I am humbled by the rich legacy of those who came before me and inspired by the boundless potential of our great nation.</p>
              <p>Ghana stands at a pivotal moment in history—a time when we must honor our ancestral wisdom while boldly embracing the opportunities of the modern world. Our cultural heritage is not merely a treasure to be preserved in museums, but a living foundation upon which we will build our future.</p>
              <p>I pledge to serve with integrity, to listen with an open heart, and to work tirelessly for the prosperity of all Ghanaians. Together, we will strengthen our communities, protect our environment, and share the unique gifts of Ghanaian culture with the world.</p>
              <p>May the wisdom of our ancestors guide us as we write this new chapter in our nation's story."</p>
            </motion.blockquote>
            
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="flex items-center justify-end mt-8"
            >
              <div className="text-right mr-3">
                <p className="font-bold text-royalBlue">His Royal Majesty</p>
                <p className="text-sm text-gray-600">King of Ghana</p>
              </div>
              <div className="text-royalGold text-3xl">
                <i className="fas fa-crown"></i>
              </div>
            </motion.div>
          </div>
        </div>
      )
    }
  }

  return (
    <section id="about" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-serif font-bold text-royalBlue mb-4">About His Royal Majesty</h2>
          <div className="w-24 h-1 bg-royalGold mx-auto mb-4"></div>
          <p className="max-w-3xl mx-auto text-gray-600">Learn about the life, vision, and royal heritage of His Majesty, the King of Ghana.</p>
        </motion.div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar */}
          <motion.div 
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="lg:w-1/3"
          >
            <div className="sticky top-24">
              {/* Royal Profile Card */}
              <div className="bg-royalBlue rounded-xl overflow-hidden shadow-xl mb-6">
                <div className="h-64 bg-royalGold/20 flex items-center justify-center">
                  <svg className="w-32 h-32" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="50" cy="40" r="20" fill="#D4AF37"/>
                    <path d="M50 65C33.4315 65 20 78.4315 20 95H80C80 78.4315 66.5685 65 50 65Z" fill="#D4AF37"/>
                    <circle cx="50" cy="50" r="45" stroke="#D4AF37" strokeWidth="2"/>
                  </svg>
                </div>
                <div className="p-6 text-center">
                  <h3 className="text-xl font-serif font-bold text-white mb-1">His Royal Majesty</h3>
                  <p className="text-royalGold mb-4">King of Ghana</p>
                  <div className="flex justify-center space-x-4 mb-6">
                    {['twitter', 'instagram', 'facebook-f'].map((social) => (
                      <a key={social} href="#" className="w-10 h-10 rounded-full bg-royalGold/20 flex items-center justify-center text-royalGold hover:bg-royalGold/30 transition-colors duration-300">
                        <i className={`fab fa-${social}`}></i>
                      </a>
                    ))}
                  </div>
                  <div className="border-t border-white/10 pt-4 space-y-2">
                    {[
                      { label: 'Coronation:', value: 'April 15, 2025' },
                      { label: 'Royal House:', value: 'Ashanti Dynasty' },
                      { label: 'Reign Begins:', value: '2025' }
                    ].map((item, index) => (
                      <div key={index} className="flex justify-between text-sm">
                        <span className="text-white/70">{item.label}</span>
                        <span className="text-white">{item.value}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Navigation Tabs */}
              <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
                <h4 className="font-bold text-royalBlue mb-4 flex items-center">
                  <i className="fas fa-link text-royalGold mr-2"></i> Quick Links
                </h4>
                <ul className="space-y-2">
                  {tabs.map((tab) => (
                    <li key={tab.id}>
                      <button
                        onClick={() => setActiveTab(tab.id)}
                        className={`w-full flex items-center text-left p-3 rounded-lg transition-colors duration-300 ${
                          activeTab === tab.id 
                            ? 'bg-royalBlue text-white' 
                            : 'text-gray-700 hover:text-royalBlue hover:bg-gray-50'
                        }`}
                      >
                        <i className={`${tab.icon} text-xs ${activeTab === tab.id ? 'text-royalGold' : 'text-royalGold'} mr-3`}></i>
                        {tab.name}
                      </button>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </motion.div>

          {/* Content */}
          <motion.div 
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="lg:w-2/3"
          >
            <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-8">
              <motion.div
                key={activeTab}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4 }}
              >
                <div className="flex items-center mb-6">
                  <div className="text-royalGold text-2xl mr-3">
                    <i className={tabs.find(tab => tab.id === activeTab)?.icon}></i>
                  </div>
                  <h3 className="text-2xl font-serif font-bold text-royalBlue">{tabContent[activeTab].title}</h3>
                </div>
                {tabContent[activeTab].content}
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default About
