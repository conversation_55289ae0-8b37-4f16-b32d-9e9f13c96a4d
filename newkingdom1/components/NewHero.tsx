'use client'

import React, { useState, useEffect } from 'react'
import type { TimeLeft } from '@/types'

const NewHero = () => {
  const [timeLeft, setTimeLeft] = useState<TimeLeft>({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  })

  useEffect(() => {
    const targetDate = new Date('2025-04-15T10:00:00').getTime()

    const timer = setInterval(() => {
      const now = new Date().getTime()
      const difference = targetDate - now

      const days = Math.floor(difference / (1000 * 60 * 60 * 24))
      const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
      const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60))
      const seconds = Math.floor((difference % (1000 * 60)) / 1000)

      setTimeLeft({ days, hours, minutes, seconds })

      if (difference < 0) {
        clearInterval(timer)
        setTimeLeft({ days: 0, hours: 0, minutes: 0, seconds: 0 })
      }
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background with gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-royalBlue via-royalBlue/95 to-royalBlue/90"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/15 via-transparent to-royalGold/5"></div>

      {/* Animated background pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="royal-pattern w-full h-full"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center text-white">
          {/* Main Title */}
          <div className="mb-8">
            <div className="text-royalGold text-6xl mb-6">
              <i className="fas fa-crown"></i>
            </div>
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-serif font-bold mb-4 leading-tight">
              Adukrom Kingdom
            </h1>
            <p className="text-xl md:text-2xl text-royalGold font-medium mb-2">
              Royal Heritage • Modern Vision
            </p>
            <p className="text-lg text-white/80 max-w-2xl mx-auto leading-relaxed">
              Celebrating centuries of tradition while building a prosperous future for all
            </p>
          </div>

          {/* Coronation Countdown */}
          <div className="mb-12">
            <h2 className="text-2xl md:text-3xl font-serif font-bold mb-6">
              Royal Coronation 2025
            </h2>
            <p className="text-white/90 mb-8">April 15, 2025 • Accra, Ghana</p>

            {/* Countdown Timer */}
            <div className="flex justify-center space-x-4 md:space-x-8 mb-8">
              {Object.entries(timeLeft).map(([unit, value]) => (
                <div
                  key={unit}
                  className="bg-white/10 backdrop-blur-lg rounded-xl p-4 md:p-6 border border-white/20 min-w-[80px] md:min-w-[100px]"
                >
                  <div className="text-2xl md:text-4xl font-bold text-royalGold mb-1">
                    {value}
                  </div>
                  <div className="text-xs md:text-sm text-white/80 capitalize font-medium">
                    {unit}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Call to Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="#coronation"
              className="px-8 py-4 bg-royalGold text-royalBlue font-bold text-lg rounded-xl hover:bg-royalGold/90 transition-all duration-300 hover:scale-105 shadow-lg"
            >
              <i className="fas fa-crown mr-2"></i>
              Learn About Coronation
            </a>
            <a
              href="#rsvp"
              className="px-8 py-4 bg-white/10 backdrop-blur-lg text-white font-bold text-lg rounded-xl hover:bg-white/20 transition-all duration-300 border border-white/30"
            >
              <i className="fas fa-calendar-check mr-2"></i>
              RSVP for Events
            </a>
          </div>

          {/* Scroll Indicator */}
          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
            <div className="animate-bounce">
              <i className="fas fa-chevron-down text-white/60 text-2xl"></i>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default NewHero
