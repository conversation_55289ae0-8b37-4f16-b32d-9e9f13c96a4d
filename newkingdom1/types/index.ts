// Common types used across the application

export interface DropdownItem {
  name: string
  href: string
}

export interface NavigationItem {
  name: string
  href?: string
  dropdown?: DropdownItem[]
}

export interface TimeLeft {
  days: number
  hours: number
  minutes: number
  seconds: number
}

export interface Feature {
  icon: string
  title: string
  description: string
  gradient: string
}

export interface FamilyMember {
  name: string
  title: string
  role: string
  description: string
  gradient: string
  icon: string
}

export interface Partner {
  name: string
  description: string
  logo: string
  category?: string
  website?: string
}

export interface Initiative {
  title: string
  description: string
  status: string
  progress: number
  icon: string
  gradient: string
  details?: string[]
  link?: string
  features?: string[]
}

export interface GalleryImage {
  title: string
  description: string
  category?: string
  imageUrl?: string
  size?: 'small' | 'medium' | 'large'
}

export interface ContactInfo {
  icon: string
  title: string
  details: string[]
  gradient: string
}

export interface SocialLink {
  icon: string
  href: string
  color: string
}
