// Form-related types

export interface RSVPFormData {
  firstName: string
  lastName: string
  email: string
  phone: string
  country: string
  events: string[]
  attendanceType: string
  reminderPreference: string
  notes: string
}

export interface ContactFormData {
  fullName: string
  email: string
  subject: string
  message: string
}

export interface AuthModalProps {
  isOpen: boolean
  onClose: () => void
  purpose: 'tickets' | 'rsvp'
}

export interface LoginFormProps {
  onClose?: () => void
  redirectTo?: string
}

export interface TicketTier {
  name: string
  price: string
  usdPrice: string
  description: string
  features: string[]
  gradient: string
  popular?: boolean
}

export interface NewsArticle {
  title: string
  excerpt: string
  date: string
  category: string
  imageUrl: string
  readTime: string
}

export interface StreamingEvent {
  title: string
  description: string
  date: string
  time: string
  status: 'live' | 'upcoming' | 'ended'
  viewers?: number
  thumbnail: string
}
