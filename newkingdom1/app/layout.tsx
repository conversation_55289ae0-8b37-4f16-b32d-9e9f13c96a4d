import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/contexts/AuthContext";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Adukrom Kingdom - Royal Heritage • Modern Vision",
  description: "Join us for the historic Royal Coronation 2025 and explore the rich cultural heritage of the Adukrom Kingdom. Experience royal traditions, modern initiatives, and Ghana's royal legacy.",
  keywords: "Adukrom Kingdom, Royal Coronation 2025, Ghana, Royal Family, Cultural Heritage, Traditional Ceremony",
  authors: [{ name: "Adukrom Kingdom" }],
  openGraph: {
    title: "Adukrom Kingdom - Royal Heritage • Modern Vision",
    description: "Join us for the historic Royal Coronation 2025 and explore the rich cultural heritage of the Adukrom Kingdom.",
    type: "website",
    locale: "en_US",
    siteName: "Adukrom Kingdom",
  },
  twitter: {
    card: "summary_large_image",
    title: "Adukrom Kingdom - Royal Heritage • Modern Vision",
    description: "Join us for the historic Royal Coronation 2025 and explore the rich cultural heritage of the Adukrom Kingdom.",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link
          rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
          integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw=="
          crossOrigin="anonymous"
          referrerPolicy="no-referrer"
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen flex flex-col`}
      >
        {/* <AuthProvider> */}
          <Header />
          <main className="flex-1">
            {children}
          </main>
          <Footer />
        {/* </AuthProvider> */}
      </body>
    </html>
  );
}
